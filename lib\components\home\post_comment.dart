import 'package:flutter/material.dart';

import '../../screens/CommentScreen/comments_screen.dart';
import '../../utils/colors.dart';
import '../size_box.dart';

Widget postComment(data, context) {
  return InkWell(
    onTap: () {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (_) => PostCommentScreen(
                    postID: data.postId!,
                  )));
    },
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        CircleAvatar(
          backgroundImage: NetworkImage(data.getPostComments![0].cPubAvatar!),
        ),
        sizedBox(0, 8),
        Expanded(
          child: Container(
            decoration: BoxDecoration(
                color: black.withOpacity(.1),
                borderRadius: BorderRadius.circular(10)),
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: <PERSON>umn(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    data.getPostComments![0].cPubName!,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    data.getPostComments![0].text!,
                  )
                ],
              ),
            ),
          ),
        )
      ],
    ),
  );
}
