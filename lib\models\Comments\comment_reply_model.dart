import 'dart:convert';

CommentReplyModel commentReplyModelFromJson(String str) => CommentReplyModel.fromJson(json.decode(str));


class CommentReplyModel {
  int? apiStatus;
  List<CommentData>? data;


  CommentReplyModel({
    this.apiStatus,
    this.data,

  });

  factory CommentReplyModel.fromJson(Map<String, dynamic> json) => CommentReplyModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<CommentData>.from(json["data"]!.map((x) => CommentData.fromJson(x))),
  );

}

class CommentData {
  String? commentID;
  String? replyID;
  String? text;
  String? commentTime;
  String? pubAvatar;
  String? pubName;
  String? pubId;
  String? isVerified;
  String? isPro;
  String? reactType;
  int? reactionCount;
  String? replayCount;
  bool? isReacted;
  bool? isAuthor;

  CommentData({
    this.commentID,
    this.replyID,
    this.text,
    this.commentTime,
    this.pubAvatar,
    this.pubName,
    this.pubId,
    this.isPro,
    this.isVerified,
    this.reactType,
    this.reactionCount,
    this.replayCount,
    this.isReacted,
    this.isAuthor,
  });

  void setIsActive(String value) {
    reactType = value;
  }


  factory CommentData.fromJson(Map<String, dynamic> json) => CommentData(
    commentID: json["comment_id"],
    replyID: json['id'],
    text: json["text"],
    commentTime: json['time'],
    pubAvatar: json['publisher']['avatar'],
    pubName: json['publisher']['name'],
    pubId: json['publisher']['user_id'],
    isVerified: json['publisher']['verified'],
    isPro: json['publisher']['is_pro'],
    reactType: json['reaction']['type'],
    reactionCount: json['reaction']['count'],
    replayCount: json['replies_count'],
    isReacted: json['reaction']['is_reacted'],
    isAuthor: json['onwer'],
  );
}
