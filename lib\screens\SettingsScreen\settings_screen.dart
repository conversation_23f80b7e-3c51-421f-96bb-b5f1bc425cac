import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import '../../components/Settings/settings_items.dart';
import '../../utils/colors.dart';

class SettingScreen extends StatelessWidget {
  const SettingScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    final profileController = Get.put(MyDataController());
    final userData = profileController.getUserDetails.value;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Settings'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            Container(
              width: size * .9,
              decoration: BoxDecoration(
                  color: white, borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10.0),
                child: ListTile(
                  contentPadding: EdgeInsets.zero,
                  leading: CircleAvatar(
                    backgroundImage: NetworkImage(userData.avatar!),
                  ),
                  title: Row(
                    children: [
                      Text(
                        userData.name!,
                        style: TextStyle(
                            color: black.withOpacity(.5),
                            fontSize: 18,
                            fontWeight: FontWeight.w500),
                      ),
                      userData.isVerified == '1'
                          ? SvgPicture.asset(
                              'assets/svg/verify.svg',
                              width: 15,
                            )
                          : Container(),
                      userData.isPro == '1'
                          ? Image.asset(
                              'assets/svg/vip.png',
                              width: 15,
                            )
                          : Container()
                    ],
                  ),
                  subtitle: Text(
                    userData.email!,
                    style: const TextStyle(color: Colors.black45, fontSize: 12),
                  ),
                ),
              ),
            ),
            sizedBox(10, 0),
            Divider(
              color: black.withOpacity(.1),
            ),
            sizedBox(10, 0),
            settingItem(
              onTap: (){
                navigate('generalSetting');
              },
                size: size,
                title: 'General Information',
                icon: 'assets/svg/person.svg'),
            sizedBox(10, 0),
            settingItem(
              onTap: (){
                navigate('securitySetting');
              },
                size: size, title: 'Security', icon: 'assets/svg/security.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: (){
                  navigate('privacySetting');
                },
                size: size, title: 'Privacy', icon: 'assets/svg/privacySetting.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: (){
                  navigate('lifeEventSetting');
                },
                size: size, title: 'Life Event', icon: 'assets/svg/event.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: (){
                  navigate('networkingSetting');
                },
                size: size,
                title: 'Networking',
                icon: 'assets/svg/network.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: (){
                  navigate('aboutSetting');
                },
                size: size,
                title: 'About',
                icon: 'assets/svg/aboutSection.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: (){
                  navigate('accountDelete');
                },
                size: size,
                title: 'Delete Account',
                icon: 'assets/svg/delete.svg'),
          ],
        ),
      ),
    );
  }
}
