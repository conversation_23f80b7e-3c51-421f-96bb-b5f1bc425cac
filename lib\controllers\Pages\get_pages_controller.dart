import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

import '../../models/Pages/get_pages_model.dart';

class GetMyPageController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var myPagesData = GetPagesModel().data.obs;

  getData({required String type}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getPages(type: type);
    isLoading.value = false;
    myPagesData.value = apiData!.data!;
    update();
  }
}

class GetLikedPagesController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var likedPageData = GetPagesModel().data.obs;

  getData({required String type, required String userID}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getPages(type: type, userID: userID);
    isLoading.value = false;
    likedPageData.value = apiData!.data!;
    update();
  }
}

class RecommendedPagesController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var recommendedPagesData = GetPagesModel().data.obs;

  getData({required String type}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getRecommended(type: type);
    isLoading.value = false;
    recommendedPagesData.value = apiData!.data!;
    update();
  }
}

