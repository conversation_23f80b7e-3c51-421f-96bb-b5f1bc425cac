import 'package:flutter_wowonder/controllers/Groups/groups_list_controller.dart';
import 'package:flutter_wowonder/controllers/Search/search_controller.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_followers_controller.dart';
import 'package:flutter_wowonder/controllers/auth/login_controller.dart';
import 'package:flutter_wowonder/controllers/comments/comment_reply_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_all_post_controller.dart';
import 'package:get/get.dart';
import '../components/home/<USER>';
import '../controllers/Groups/member_request_controller.dart';
import '../controllers/Pages/get_pages_controller.dart';
import '../controllers/Pages/pages_details_controller.dart';
import '../controllers/Story/get_all_stories.dart';
import '../controllers/UserData/get_user_data_controller.dart';
import '../controllers/auth/pw_token_controller.dart';
import '../controllers/bottom_bar_controller.dart';
import '../controllers/comments/get_all_comments_controller.dart';
import '../controllers/posts/get_page_post_controller.dart';
import '../controllers/posts/get_user_post_controller.dart';
import '../controllers/posts/get_vide_post_controller.dart';
import '../controllers/posts/post_action_controller.dart';
import '../controllers/posts/saved_post_controller.dart';

class ControllerBindings extends Bindings {
  @override
  void dependencies() {
    Get.put<LoginController>(LoginController());
    Get.lazyPut(() => GetAllPostController());
    Get.lazyPut<BottomBarController>(() => BottomBarController());
    Get.lazyPut<GetUserFollowersController>(() => GetUserFollowersController());
    Get.lazyPut<GetUserPostController>(() => GetUserPostController());
    Get.lazyPut<GetAllStoriesController>(() => GetAllStoriesController());
    Get.lazyPut<GetAllPostCommentController>(
        () => GetAllPostCommentController());
    Get.lazyPut<CommentReplyController>(() => CommentReplyController());
    Get.lazyPut<PostActionController>(() => PostActionController());
    Get.lazyPut<MyDataController>(() => MyDataController());
    Get.lazyPut<GetMyGroupsController>(() => GetMyGroupsController());
    Get.lazyPut<MemberRequestController>(() => MemberRequestController());
    Get.lazyPut<GetJoinedGroupsController>(() => GetJoinedGroupsController());
    Get.lazyPut<SuggestedGroupsController>(() => SuggestedGroupsController());
    Get.lazyPut<SearchResultsController>(() => SearchResultsController());
    Get.lazyPut<UserDataController>(() => UserDataController());
    Get.lazyPut<GetMyPageController>(() => GetMyPageController());
    Get.lazyPut<GetLikedPagesController>(() => GetLikedPagesController());
    Get.lazyPut<RecommendedPagesController>(() => RecommendedPagesController());
    Get.lazyPut<PageDetailsController>(() => PageDetailsController());
    Get.lazyPut<GetPagePostController>(() => GetPagePostController());
    Get.lazyPut<GetVideoPostController>(() => GetVideoPostController());
    Get.lazyPut<SavedPostController>(() => SavedPostController());
    final controller = Get.put(TKController());
    controller.getData();
    //getInit();
  }
}
