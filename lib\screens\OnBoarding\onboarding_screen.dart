import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/introduction.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class OnBoarding extends StatefulWidget {
  const OnBoarding({Key? key}) : super(key: key);

  @override
  State<OnBoarding> createState() => _OnBoardingState();
}

class _OnBoardingState extends State<OnBoarding> {
  final PageController controller = PageController();
  bool isLastPage = false;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: background,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        automaticallyImplyLeading: false,
        elevation: 0,
        backgroundColor: Colors.transparent,
        actions: [
          isLastPage
              ? Text(emptyText)
              : TextButton(
                  onPressed: () {
                    controller.jumpToPage(2);
                  },
                  child: Text(
                    skip,
                    style: TextStyle(
                        color: black.withOpacity(0.5),
                        fontSize: 16,
                        fontWeight: FontWeight.w300),
                  ))
        ],
      ),
      body: Stack(
        children: [
          PageView(
            onPageChanged: (index) {
              setState(() {
                isLastPage = (index == 2);
              });
            },
            controller: controller,
            children: [
              IntroScreen1(
                image: onBoard1,
                title: onTitle1,
                dec: onDec1,
              ),
              IntroScreen1(
                image: onBoard2,
                title: onTitle2,
                dec: onDec2,
              ),
              IntroScreen1(
                image: onBoard3,
                title: onTitle3,
                dec:
                    onDec3,
              ),
            ],
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                  alignment: const Alignment(0, 0.75),
                  child: SmoothPageIndicator(
                    effect: WormEffect(
                      paintStyle: PaintingStyle.stroke,
                      type: WormType.thin,
                      activeDotColor: primary,
                      dotColor: primary.withOpacity(0.5),
                    ),
                    controller: controller,
                    count: 3,
                  )),
              sizedBox(20, 0),
              isLastPage
                  ? CustomButton(
                      title: getStart,
                      onTap: () {
                        navigate('login');
                      },
                    )
                  : Text(emptyText),
              sizedBox(40, 0),
            ],
          )
        ],
      ),
    );
  }
}
