import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/PostModels/get_all_post_model.dart';

class GetVideoPostController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  RxList<Datum> dataList = <Datum>[].obs;
  var lastID = '0';

  @override
  void onInit() {
    super.onInit();
    getData();
    onRefresh();
  }

  getData({String? tokenID, String? afterPostID, String? type}) async {
    isLoading.value = true;
    var apiData = await apiServices.getAllPost(
        tokenID: tokenID, afterPostID: lastID, postType: 'video');
    lastID = apiData!.data!.last.postId!;
    update();
    dataList.value = apiData.data!;
    isLoading.value = false;
    update();
  }

  Future <void> onRefresh() async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 3));
    var list =
        await APISERvices().getAllPost(afterPostID: '0', postType: 'video');
    dataList.clear();
    dataList.addAll(list!.data!);
    isLoading.value = false;
    update();
  }

  Future<void> onLoading() async {
    await Future.delayed(const Duration(seconds: 3));
    var list =
        await APISERvices().getAllPost(afterPostID: lastID, postType: 'video');
    dataList.addAll(list!.data!);
    lastID = list.data!.last.postId!;
    update();
  }

  statusChange(String id, String status, Datum getCountries, int index,
      {String? ccc}) async {
    await apiServices.postReaction(postID: id, action: status, count: ccc);

    getCountries.setIsActive(ccc.toString());
    dataList[index] = getCountries;
    update();
  }
}
