import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/Search/search_results_model.dart';

class SearchResultsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var searchData = SearchResultsModel().obs;

  getData({String? searchText}) async {
    isLoading.value = true;
    var apiData = await APISERvices.searchResult(searchText: searchText ?? '');
    searchData.value = apiData!;
    isLoading.value = false;
    update();
  }
}


