import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/PostModels/get_user_post_model.dart';

class GetUserPostController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  RxList<UserDatum> dataList = <UserDatum>[].obs;

  getData({required String userID}) async {
    isLoading.value = true;
    var apiData = await apiServices.getUserPost(
      userID: userID
    );
    dataList.value = apiData!.data!;
    isLoading.value = false;
    update();
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 3));
    var list = await APISERvices().getUserPost();
    dataList.clear();
    dataList.addAll(list!.data!);
    isLoading.value = false;
    update();
  }

  statusChange(String id, String status, UserDatum getCountries, int index,
      {String? ccc}) async {
    await apiServices.postReaction(postID: id, action: status, count: ccc);

    getCountries.setIsActive(ccc.toString());
    dataList[index] = getCountries;
    update();
  }
}
