# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: "7cf60b9f0cc88203c5a190b4cd62a99feea42759a7fa695010eb5de1c0b2252a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: "58082b3f0dca697204dbab0ef9ff208bfaea7767ea771076af9a343488428dda"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.3"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "758e6d74e971c3e5aceb4110bfd6698efc7f501675bcfe0c775459a8140750eb"
      url: "https://pub.dev"
    source: hosted
    version: "2.13.0"
  audioplayers:
    dependency: "direct main"
    description:
      name: audioplayers
      sha256: "752039d6aa752597c98ec212e9759519061759e402e7da59a511f39d43aa07d2"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: de576b890befe27175c2f511ba8b742bec83765fa97c3ce4282bba46212f58e4
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: e507887f3ff18d8e5a10a668d7bedc28206b12e10b98347797257c6ae1019c3b
      url: "https://pub.dev"
    source: hosted
    version: "6.0.0"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "3d3d244c90436115417f170426ce768856d8fe4dfc5ed66a049d2890acfa82f9"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "6834dd48dfb7bc6c2404998ebdd161f79cd3774a7e6779e1348d54a3bfdcfaa5"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: db8fc420dadf80da18e2286c18e746fb4c3b2c5adbf0c963299dde046828886d
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "8605762dddba992138d476f6a0c3afd9df30ac5b96039929063eceed416795c2"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  awesome_notifications:
    dependency: "direct main"
    description:
      name: awesome_notifications
      sha256: d9e46ce7f5171ee1e9b1c5bc6dc40bd77528561f592842ce97ce3a0a9ae155ef
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  awesome_notifications_fcm:
    dependency: "direct main"
    description:
      name: awesome_notifications_fcm
      sha256: "1b116e7069f61c10ec25de15cd8f67912db3fc8bd7a5e2e9ed8013cb95633c59"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  better_player:
    dependency: "direct main"
    description:
      path: "."
      ref: Issue1057
      resolved-ref: e45f447b6d3ae6a3404ddfd9663f8aeae864ddec
      url: "https://github.com/matthewrice345/betterplayer.git"
    source: git
    version: "0.0.83"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "8aab1771e1243a5063b8b0ff68042d67334e3feab9e95b9490f9a6ebf73b42ea"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  chewie:
    dependency: "direct main"
    description:
      name: chewie
      sha256: "745e81e84c6d7f3835f89f85bb49771c0a66099e4caf8f8e9e9a372bc66fb2c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "55d7b444feb71301ef6b8838dbc1ae02e63dd48c8773f3810ff53bb1e2945b32"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  encrypt:
    dependency: "direct main"
    description:
      name: encrypt
      sha256: "62d9aa4670cc2a8798bab89b39fc71b6dfbacf615de6cf5001fb39f7e4a996a2"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "493f37e7df1804778ff3a53bd691d8692ddf69702cf4c1c1096a2e41b4779e21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "5fc22d7c25582e38ad9a8515372cd9a93834027aacf1801cf01164dac0ffa08c"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: f42eacb83b318e183b1ae24eead1373ab1334084404c8c16e0354f9a3e55d385
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "1003a5a03a61fc9a22ef49f37cbcb9e46c86313a7b2e7029b9390cf8c6fc32cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "6643fe3dbd021e6ccfb751f7882b39df355708afbdeb4130fc50f9305a9d1a3d"
      url: "https://pub.dev"
    source: hosted
    version: "2.17.2"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "9897c01efaa950d2f6da8317d12452749a74dc45f33b46390a14cfe28067f271"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.7"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "16a71e08fbf6e00382816e1b13397898c29a54fa0ad969c2c2a3b82a704877f0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.35"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_colorpicker:
    dependency: transitive
    description:
      name: flutter_colorpicker
      sha256: "969de5f6f9e2a570ac660fb7b501551451ea2a1ab9e2097e89475f60e07816ea"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter_html:
    dependency: "direct main"
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "3f41d009ba7172d5ff9be5f6e6e6abb4300e263aab8866d2a0842ed2a70f8f0c"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: c6b0b4c05c458e1c01ad9bcc14041dd7b1f6783d487be4386f793f47a8a4d03e
      url: "https://pub.dev"
    source: hosted
    version: "2.0.20"
  flutter_speed_dial:
    dependency: "direct main"
    description:
      name: flutter_speed_dial
      sha256: "698a037274a66dbae8697c265440e6acb6ab6cae9ac5f95c749e7944d8f28d41"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "7b4ca6cf3304575fe9c8ec64813c8d02ee41d2afe60bcfe0678bcb5375d596a2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.10+1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: e8f4f8b461a140ffb7c71f938bc76efc758893e7468843d9dbf70cb0b9e900cb
      url: "https://pub.dev"
    source: hosted
    version: "0.8.5+3"
  fwfh_text_style:
    dependency: transitive
    description:
      name: fwfh_text_style
      sha256: "5f8b587fd223a6bf14aad3d3da5e7ced0628becbd0768f8e7ae25ff6b9f3d2ec"
      url: "https://pub.dev"
    source: hosted
    version: "2.23.8"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  google_fonts:
    dependency: transitive
    description:
      name: google_fonts
      sha256: "2776c66b3e97c6cdd58d1bd3281548b074b64f1fd5c8f82391f7456e38849567"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.5"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: "761a297c042deedc1ffbb156d6e2af13886bb305c2a343a4d972504cd67dd938"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image_cropper:
    dependency: "direct main"
    description:
      name: image_cropper
      sha256: ee0485e232ca7e41f9397526bd911feb1996440e1e14e3d5f5240664fa435936
      url: "https://pub.dev"
    source: hosted
    version: "7.0.5"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: ae3a307f970d1d2c286bce4519f8cb20c3f0a5332867a912366e1843e24bc8d3
      url: "https://pub.dev"
    source: hosted
    version: "5.0.4"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: "04d0eebd5f901a4109f486d211c888f1fb25c6b70704a405fb1505cb742b05f0"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.3"
  image_picker:
    dependency: "direct main"
    description:
      name: image_picker
      sha256: "021834d9c0c3de46bf0fe40341fa07168407f694d9b2bb18d532dc1261867f7a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "4161e1f843d8480d2e9025ee22411778c3c9eb7e40076dcf2da23d8242b7b51c"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+3"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "5d6eb13048cd47b60dbf1a5495424dea226c5faf3950e20bf8120a58efb5b5f3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.4"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "6703696ad49f5c3c8356d576d7ace84d1faf459afb07accbb0fae780753ff447"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: d6f56758b7d3014a48af9701c085700aac781a92a87a62b1333b46d8879661cf
      url: "https://pub.dev"
    source: hosted
    version: "0.19.0"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "6bb818ecbdffe216e81182c2f0714a2e62b593f4a4f13098713ff1685dfb6ab0"
      url: "https://pub.dev"
    source: hosted
    version: "10.0.9"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: f8b613e7e6a13ec79cfdc0e97638fddb3ab848452eff057653abd3edba760573
      url: "https://pub.dev"
    source: hosted
    version: "3.0.9"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "6ba465d5d76e67ddf503e1161d1f4a6bc42306f9d66ca1e8f079a47290fb06d3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  like_button:
    dependency: "direct main"
    description:
      name: like_button
      sha256: "08e6a45b78888412df5d351786c550205ad3a677e72a0820d5bbc0b063c8a463"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "976c774dd944a42e83e2467f4cc670daef7eed6295b10b36ae8c85bcbf828235"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "6a24ade5d3d918c306bb1c21a6b9a04aab0489d51a2582522eea820b4093b62b"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  media_picker_widget:
    dependency: "direct main"
    description:
      name: media_picker_widget
      sha256: "0058a3b050df8e9aeebec300df906ddc31127ae9612085146b0e7c01b316800c"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8-pre.1"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "2e123074287cc9fd6c09de8336dae606d1ddb88d9ac47358826db698c176a1f2"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  onesignal_flutter:
    dependency: "direct main"
    description:
      name: onesignal_flutter
      sha256: "912ba6bbaf6208509372ce2c172b3cc7c5a3d08fa95001d39b4fe15c3d7e0556"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: c9e7d3a4cd1410877472158bee69963a4579f78b68c65a2b7d40d1a7a88bb161
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: bca87b0165ffd7cdb9cad8edd22d18d2201e886d9a9f19b4fb3452ea7df3a72a
      url: "https://pub.dev"
    source: hosted
    version: "2.2.6"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  photo_manager:
    dependency: "direct main"
    description:
      name: photo_manager
      sha256: "68d6099d07ce5033170f8368af8128a4555cf1d590a97242f83669552de989b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "9b71283fc13df574056616011fb138fd3b793ea47cc509c189a6c3fa5f8a1a65"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.5"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "4be0097fcf3fd3e8449e53730c631200ebc7b88016acecab2b0da2f0149222fe"
      url: "https://pub.dev"
    source: hosted
    version: "3.9.1"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  refresh_loadmore:
    dependency: "direct main"
    description:
      name: refresh_loadmore
      sha256: "938c40a612de2517143c08412170286b583f1319857479b9bdeb8a2db458b513"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: d3bbe5553a986e83980916ded2f0b435ef2e1893dfaa29d5a7a790d0eca12180
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "93d0ec9dd902d85f326068e6a899487d1f65ffcd5798721a95330b26c8131577"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "0a8a893bf4fd1152f93fec03a415d11c27c74454d96e2318a7ac38dd18683ab7"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "22e2ecac9419b4246d7c22bfbbda589e3acf5c0351137d87dd2939d984d37c3b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "9aee1089b36bd2aafe06582b7d7817fd317ef05fc30e6ba14bff247d0933042a"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel:
    dependency: transitive
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  smooth_page_indicator:
    dependency: "direct main"
    description:
      name: smooth_page_indicator
      sha256: "725bc638d5e79df0c84658e1291449996943f93bacbc2cec49963dbbab48d8ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "254ee5351d6cb365c859e20ee823c3bb479bf4a293c22d17a9f1bf144ce86f7c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  story:
    dependency: "direct main"
    description:
      name: story
      sha256: "0cff3c02d5ad1d9c1cf79481b8fe4a4f2f859e56b351644e96b8b209e6a110a5"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  story_creator:
    dependency: "direct main"
    description:
      name: story_creator
      sha256: "9b9426ba95f78e06a38ec4d4892d707f1ccd49b83836e8df4225b40644a6c43a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "921cd31725b72fe181906c6a94d987c78e3b98c2e205b397ea399d4054872b43"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: "7f554798625ea768a7518313e58f83891c7f5024f88e46e7182a4558850a4b8e"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: fb31f383e2ee25fbbfe06b40fe21e1e458d14080e3c67e7ba0acfde4df4e0bbd
      url: "https://pub.dev"
    source: hosted
    version: "0.7.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: ceb2625f0c24ade6ef6778d1de0b2e44f2db71fded235eb52295247feba8c5cf
      url: "https://pub.dev"
    source: hosted
    version: "6.3.3"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "7068716403343f6ba4969b4173cbf3b84fc768042124bc2c011e5d782b24fe89"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "9a1a42d5d2d95400c795b2914c36fdcb525870c752569438e4ebb09a2b5d90de"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "8d9e750d8c9338601e709cd0885f95825086bd8b642547f26bda435aade95d8a"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: ecf9725510600aa2bb6d7ddabe16357691b6d2805f66216a97d1b881e21beff7
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "814e9e88f21a176ae1359149021870e87f7cddaf633ab678a5d2b0bff7fd1ba8"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.0"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "32c3c684e02f9bc0afb0ae0aa653337a2fe022e8ab064bcd7ffda27a74e288e3"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: c86987475f162fadff579e7320c7ddda04cd2fdeffbe1129227a85d9ac9e03da
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "12faff3f73b1741a36ca7e31b292ddeb629af819ca9efe9953b70bd63fc8cd81"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: aced48e701e24c02b0b7f881a8819e4937794e46b5a5821005e2bf3b40a324cc
      url: "https://pub.dev"
    source: hosted
    version: "2.8.7"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "9529001630e42988f755772972d5014d30121610700e8e502278a245939f8fc8"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: d1e9a824f2b324000dc8fb2dcb2a3285b6c1c7c487521c63306cc5b394f68a7c
      url: "https://pub.dev"
    source: hosted
    version: "2.6.1"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "236454725fafcacf98f0f39af0d7c7ab2ce84762e3b63f2cbb3ef9a7e0550bc6"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: ff4d69a6614b03f055397c27a71c9d3ddea2b2a23d71b2ba0164f59ca32b8fe2
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: "15c54a459ec2c17b4705450483f3d5a2858e733aee893dcee9d75fd04814940d"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: ddfa8d30d89985b96407efce8acbdd124701f96741f2d981ca860662f1c0dc02
      url: "https://pub.dev"
    source: hosted
    version: "15.0.0"
  wakelock:
    dependency: "direct main"
    description:
      name: wakelock
      sha256: "769ecf42eb2d07128407b50cb93d7c10bd2ee48f0276ef0119db1d25cc2f87db"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.2"
  wakelock_macos:
    dependency: transitive
    description:
      name: wakelock_macos
      sha256: "047c6be2f88cb6b76d02553bca5a3a3b95323b15d30867eca53a19a0a319d4cd"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      sha256: "1f4aeb81fb592b863da83d2d0f7b8196067451e4df91046c26b54a403f9de621"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  wakelock_web:
    dependency: transitive
    description:
      name: wakelock_web
      sha256: "1b256b811ee3f0834888efddfe03da8d18d0819317f20f6193e2922b41a501b5"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  wakelock_windows:
    dependency: transitive
    description:
      name: wakelock_windows
      sha256: "857f77b3fe6ae82dd045455baa626bc4b93cb9bb6c86bf3f27c182167c3a5567"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1"
  web:
    dependency: transitive
    description:
      name: web
      sha256: "97da13628db363c635202ad97068d47c5b8aa555808e7a9411963c533b449b27"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: a6f0236dbda0f63aa9a25ad1ff9a9d8a4eaaa5012da0dc59d21afdb1dc361ca4
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
  zoom_tap_animation:
    dependency: "direct main"
    description:
      name: zoom_tap_animation
      sha256: d9f7a73cab65aa1546ba6886b5e21d3c8ccccb34e4e5f770301c306d4868bee0
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
sdks:
  dart: ">=3.7.0-0 <=3.10.1"
  flutter: ">=3.22.0"
