import 'package:flutter_wowonder/models/Comments/comment_reply_model.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

class CommentReplyController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  final commentData = CommentReplyModel().data.obs;

  getData({required String type, required String commentID}) async {
    isLoading.value = true;
    var apiData = await apiServices.commentReply(type: type, commentID: commentID);
    commentData.value = apiData!.data!;
    isLoading.value = false;
    update();
  }
}
