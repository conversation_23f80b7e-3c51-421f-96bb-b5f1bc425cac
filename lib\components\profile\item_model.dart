import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';

import '../../utils/colors.dart';
Widget infoItem ({
  required String title,
  required String value,
  String? schoolCompleted,
  double? size
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      schoolCompleted == '1' ? Row(
      children: [
  Text(title,
    style: const TextStyle(
        fontSize: 16
    ),),
  sizedBox(0, 5),
  Container(
  width: size! * .3,
    decoration: BoxDecoration(
      color: primary,
      borderRadius: BorderRadius.circular(10)
    ),
    child: const Center(
      child: Text('Completed',
      style: TextStyle(
        color: white
      ),),
    ),
  )
  ],
  ) : Text(title,
      style: const TextStyle(
        fontSize: 16
      ),),
      Text(value,
      style: TextStyle(
        color: black.withOpacity(.5)
      ),)
    ],
  );
}