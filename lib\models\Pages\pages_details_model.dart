import 'dart:convert';

PageDetailsModel pageDetailsModelFromJson(String str) => PageDetailsModel.fromJson(json.decode(str));

String pageDetailsModelToJson(PageDetailsModel data) => json.encode(data.toJson());

class PageDetailsModel {
  int? apiStatus;
  PageData? pageData;

  PageDetailsModel({
    this.apiStatus,
    this.pageData,
  });

  factory PageDetailsModel.fromJson(Map<String, dynamic> json) => PageDetailsModel(
    apiStatus: json["api_status"],
    pageData: json["page_data"] == null ? null : PageData.fromJson(json["page_data"]),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "page_data": pageData?.toJson(),
  };
}

class PageData {
  String? pageId;
  String? userId;
  String? pageName;
  String? pageTitle;
  String? pageDescription;
  String? avatar;
  String? cover;
  String? usersPost;
  String? pageCategory;
  String? subCategory;
  String? website;
  String? facebook;
  String? google;
  String? vk;
  String? twitter;
  String? linkedin;
  String? company;
  String? phone;
  String? address;
  String? callActionType;
  String? callActionTypeUrl;
  String? backgroundImage;
  String? instgram;
  String? youtube;
  String? verified;
  String? active;
  String? registered;
  String? boosted;
  String? time;
  String? about;
  String? url;
  String? name;
  int? rating;
  String? category;
  String? pageSubCategory;
  bool? isReported;
  bool? isPageOnwer;
  String? username;
  List<dynamic>? fields;
  String? postCount;
  bool? isLiked;
  String? likesCount;
  String? callActionTypeText;
  bool? isRated;
  List<dynamic>? adminInfo;

  PageData({
    this.pageId,
    this.userId,
    this.pageName,
    this.pageTitle,
    this.pageDescription,
    this.avatar,
    this.cover,
    this.usersPost,
    this.pageCategory,
    this.subCategory,
    this.website,
    this.facebook,
    this.google,
    this.vk,
    this.twitter,
    this.linkedin,
    this.company,
    this.phone,
    this.address,
    this.callActionType,
    this.callActionTypeUrl,
    this.backgroundImage,
    this.instgram,
    this.youtube,
    this.verified,
    this.active,
    this.registered,
    this.boosted,
    this.time,
    this.about,
    this.url,
    this.name,
    this.rating,
    this.category,
    this.pageSubCategory,
    this.isReported,
    this.isPageOnwer,
    this.username,
    this.fields,
    this.postCount,
    this.isLiked,
    this.likesCount,
    this.callActionTypeText,
    this.isRated,
    this.adminInfo,
  });

  void setIsLiked(bool value) {
    isLiked = value;
  }

  factory PageData.fromJson(Map<String, dynamic> json) => PageData(
    pageId: json["page_id"],
    userId: json["user_id"],
    pageName: json["page_name"],
    pageTitle: json["page_title"],
    pageDescription: json["page_description"],
    avatar: json["avatar"],
    cover: json["cover"],
    usersPost: json["users_post"],
    pageCategory: json["page_category"],
    subCategory: json["sub_category"],
    website: json["website"],
    facebook: json["facebook"],
    google: json["google"],
    vk: json["vk"],
    twitter: json["twitter"],
    linkedin: json["linkedin"],
    company: json["company"],
    phone: json["phone"],
    address: json["address"],
    callActionType: json["call_action_type"],
    callActionTypeUrl: json["call_action_type_url"],
    backgroundImage: json["background_image"],
    instgram: json["instgram"],
    youtube: json["youtube"],
    verified: json["verified"],
    active: json["active"],
    registered: json["registered"],
    boosted: json["boosted"],
    time: json["time"],
    about: json["about"],
    url: json["url"],
    name: json["name"],
    rating: json["rating"],
    category: json["category"],
    pageSubCategory: json["page_sub_category"],
    isReported: json["is_reported"],
    isPageOnwer: json["is_page_onwer"],
    username: json["username"],
    fields: json["fields"] == null ? [] : List<dynamic>.from(json["fields"]!.map((x) => x)),
    postCount: json["post_count"],
    isLiked: json["is_liked"],
    likesCount: json["likes_count"],
    callActionTypeText: json["call_action_type_text"],
    isRated: json["is_rated"],
    adminInfo: json["admin_info"] == null ? [] : List<dynamic>.from(json["admin_info"]!.map((x) => x)),
  );

  Map<String, dynamic> toJson() => {
    "page_id": pageId,
    "user_id": userId,
    "page_name": pageName,
    "page_title": pageTitle,
    "page_description": pageDescription,
    "avatar": avatar,
    "cover": cover,
    "users_post": usersPost,
    "page_category": pageCategory,
    "sub_category": subCategory,
    "website": website,
    "facebook": facebook,
    "google": google,
    "vk": vk,
    "twitter": twitter,
    "linkedin": linkedin,
    "company": company,
    "phone": phone,
    "address": address,
    "call_action_type": callActionType,
    "call_action_type_url": callActionTypeUrl,
    "background_image": backgroundImage,
    "instgram": instgram,
    "youtube": youtube,
    "verified": verified,
    "active": active,
    "registered": registered,
    "boosted": boosted,
    "time": time,
    "about": about,
    "url": url,
    "name": name,
    "rating": rating,
    "category": category,
    "page_sub_category": pageSubCategory,
    "is_reported": isReported,
    "is_page_onwer": isPageOnwer,
    "username": username,
    "fields": fields == null ? [] : List<dynamic>.from(fields!.map((x) => x)),
    "post_count": postCount,
    "is_liked": isLiked,
    "likes_count": likesCount,
    "call_action_type_text": callActionTypeText,
    "is_rated": isRated,
    "admin_info": adminInfo == null ? [] : List<dynamic>.from(adminInfo!.map((x) => x)),
  };
}
