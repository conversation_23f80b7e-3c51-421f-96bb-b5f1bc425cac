import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/profile_screen.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:get/get.dart';

import '../../components/Shimmer/shimmer_effect.dart';
import '../../controllers/Search/search_controller.dart';
import '../../utils/colors.dart';

class UserSearchResults extends StatefulWidget {
  const UserSearchResults({Key? key}) : super(key: key);

  @override
  State<UserSearchResults> createState() => _UserSearchResultsState();
}

class _UserSearchResultsState extends State<UserSearchResults> {
  final controller = Get.put(SearchResultsController());

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Obx(() {
      if (controller.isLoading.value) {
        return searchResultShimmer(size);
      } else {
        if (controller.searchData.value.users!.isEmpty) {
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  noData,
                  width: size.width * .5,
                ),
                const Text(
                  'No data!',
                  style: TextStyle(color: primary, fontSize: 30),
                ),
                const Text('There are no data available!')
              ],
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.all(10.0),
            child: ListView.builder(
                shrinkWrap: true,
                primary: false,
                itemCount: controller.searchData.value.users!.length,
                itemBuilder: (_, index) {
                  final data = controller.searchData.value.users![index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: Container(
                      width: size.width * .9,
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                              color: black.withOpacity(.100), blurRadius: 1)
                        ],
                        color: white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15.0,
                          vertical: 8,
                        ),
                        child: Row(
                          children: [
                            CircleAvatar(
                              radius: 30,
                              backgroundImage: NetworkImage(data.avatar!),
                            ),
                            sizedBox(0, 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      data.name!,
                                      style: const TextStyle(
                                          color: black,
                                          fontSize: 19,
                                          fontWeight: FontWeight.w400),
                                    ),
                                    data.verified == '1'
                                        ? SvgPicture.asset(
                                            'assets/svg/verify.svg',
                                            width: 18,
                                          )
                                        : Container(),
                                    data.isPro == '1'
                                        ? Image.asset('assets/svg/vip.png',
                                            width: 20)
                                        : Container()
                                  ],
                                ),
                                Row(
                                  children: [
                                    data.address == ''
                                        ? Container()
                                        : Text('Lives in ${data.address} •'),
                                    sizedBox(0, 5),
                                    Text(
                                        '${data.details!.followersCount} followers'),
                                  ],
                                ),
                                InkWell(
                                  onTap: (){
                                    pageRoute(context, MyProfileScreen(userID: data.userId!,));
                                  },
                                  child: Container(
                                    width: size.width * .650,
                                    decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(10),
                                        color: primary),
                                    child: const Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.0),
                                      child: Center(
                                        child: Text(
                                          'View Profile',
                                          style: TextStyle(color: white),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          );
        }
      }
    });
  }
}
