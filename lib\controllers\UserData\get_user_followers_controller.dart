import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/UserData/get_user_followers_model.dart';

class GetUserFollowersController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var followersData = UserFollowersModel().obs;

  getData({String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    isLoading.value = true;
    final apiData = await apiServices.getUserFollowers(userID: userID ?? id);
    isLoading.value = false;
    followersData.value = apiData!;
    update();
  }
}
