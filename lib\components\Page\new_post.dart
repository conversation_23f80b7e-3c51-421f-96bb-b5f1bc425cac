import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../controllers/posts/get_page_post_controller.dart';
import '../../models/Pages/pages_details_model.dart';
import '../../screens/AddNewPost/page_new_post.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';
import '../size_box.dart';

Widget newPost({
  required BuildContext context,
  required double size,
  required PageData pageData,
  required pageID,
  required String name,
  required String avatar,
  required GetPagePostController controller,
}) {
  return Center(
    child: InkWell(
      onTap: () async {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (_) => NewPagePost(
                  pageID: pageID,
                  name: name,
                  avatar: avatar,
                )));
      },
      child: Container(
        width: size * .9,
        height: 180,
        decoration: BoxDecoration(
          color: white,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Divider(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 35,
                    width: 35,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        image: DecorationImage(
                            image: NetworkImage(pageData.avatar!),
                            fit: BoxFit.cover)),
                  ),
                  Center(
                    child: Text(
                      inMind,
                    ),
                  ),
                  Container(
                    height: 20,
                    width: 20,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      'assets/svg/smile.svg',
                    ),
                  ),
                ],
              ),
              const Divider(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                      child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/camera_icon.svg',
                              width: 17,
                            ),
                            Text(
                              'Photos',
                              style: TextStyle(
                                  color: black.withOpacity(0.5), fontSize: 13),
                            )
                          ],
                        ),
                      ),
                    ),
                  )),
                  sizedBox(0, 5),
                  Expanded(
                      child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/video_icon.svg',
                              width: 17,
                            ),
                            Text(
                              'Videos',
                              style: TextStyle(
                                  color: black.withOpacity(0.5), fontSize: 13),
                            )
                          ],
                        ),
                      ),
                    ),
                  )),
                  sizedBox(0, 5),
                  Expanded(
                      child: Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Center(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/smile.svg',
                              width: 17,
                            ),
                            Text(
                              'Feelings',
                              style: TextStyle(
                                  color: black.withOpacity(0.5), fontSize: 13),
                            )
                          ],
                        ),
                      ),
                    ),
                  )),
                ],
              ),
              sizedBox(10, 0),
            ],
          ),
        ),
      ),
    ),
  );
}
