import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';
import '../../widgets/button_widget.dart';

class AboutSetting extends StatefulWidget {
  const AboutSetting({Key? key}) : super(key: key);

  @override
  State<AboutSetting> createState() => _AboutSettingState();
}

class _AboutSettingState extends State<AboutSetting> {
  final about = TextEditingController();
  final profile = Get.put(UserDataController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'About'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
          vertical: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                  border: Border.all(
                    color: black.withOpacity(.1),
                  ),
                  borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: TextField(
                  controller: about,
                  keyboardType: TextInputType.multiline,
                  minLines: 2,
                  maxLines: 10,
                  decoration: const InputDecoration(
                      border: InputBorder.none,
                      label: Text('About You'),
                      hintText: 'Describe about you!'),
                ),
              ),
            ),
            sizedBox(20, 0),
            CustomButton(
              title: 'Save',
              onTap: () {
                APISERvices.updateAbout(
                  context: context,
                  about: about.text,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
