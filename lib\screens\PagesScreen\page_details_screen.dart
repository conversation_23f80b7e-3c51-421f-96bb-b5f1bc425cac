// ignore_for_file: depend_on_referenced_packages

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/controllers/posts/post_action_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/widgets/inside_page_appbar.dart';
import 'package:get/get.dart';
import '../../components/Page/about_section.dart';
import '../../components/Page/header_button.dart';
import '../../components/Page/new_post.dart';
import '../../components/Page/page_details_section.dart';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../controllers/Pages/pages_details_controller.dart';
import '../../controllers/posts/get_page_post_controller.dart';
import '../../utils/colors.dart';
import 'package:path/path.dart' as path;

class PageDetailsScreen extends StatefulWidget {
  final String pageID;

  const PageDetailsScreen({Key? key, required this.pageID}) : super(key: key);

  @override
  State<PageDetailsScreen> createState() => _PageDetailsScreenState();
}

class _PageDetailsScreenState extends State<PageDetailsScreen> {
  final detailController = Get.put(PageDetailsController());
  final controller = Get.put(GetPagePostController());
  final postActionController = Get.put(PostActionController());
  final pageController = PageController();
  final profileDetailsController = Get.put(UserDataController());
  final reportTextController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    detailController.getData(pageID: widget.pageID);
    controller.getData(pageID: widget.pageID);
  }

  Future<void> onRefresh ()async {
    controller.getData(pageID: widget.pageID);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    final shimmerSize = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      extendBodyBehindAppBar: true,
      appBar: insidePageAppBar(
          size: size,
          title: '',
          context: context,
          onSelected: (value) {
            switch (value) {
              case 0:
                Get.defaultDialog(
                    title: 'Report',
                    content: Form(
                      key: formKey,
                      child: Container(
                        width: size * .9,
                        decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(.1),
                            borderRadius: BorderRadius.circular(20)),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15.0),
                          child: Center(
                            child: TextFormField(
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return 'Please write something wrong about this page';
                                }
                                return null;
                              },
                              keyboardType: TextInputType.multiline,
                              minLines: 2,
                              maxLines: 5,
                              controller: reportTextController,
                              decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'What\'s wrong with this page?'),
                            ),
                          ),
                        ),
                      ),
                    ),
                    textCancel: 'Cancel',
                    textConfirm: 'Send Report',
                    cancelTextColor: primary,
                    confirmTextColor: white,
                    onCancel: () {
                      reportTextController.clear();
                    },
                    onConfirm: () {
                      if (formKey.currentState!.validate()) {
                        APISERvices.reportPage(
                          pageID: widget.pageID,
                          text: reportTextController.text.toString(),
                        );
                        reportTextController.clear();
                        Navigator.pop(context);
                        Get.snackbar(
                          'Report Sent',
                          'Your report has been sent to our team successfully.',
                        );
                      } else {
                        return;
                      }
                    });
                break;
              case 1:
                Clipboard.setData(
                    ClipboardData(text: detailController.pageData.value.url!));
                Get.snackbar('Copied', 'Page link copied successfully!');
            }
          },
          menuItem: [
            const PopupMenuItem(value: 0, child: Text('Report Page')),
            const PopupMenuItem(value: 1, child: Text('Copy link')),
          ]),
      body:
      RefreshIndicator(
        onRefresh: onRefresh,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Obx(() {
                if (detailController.isLoading.value) {
                  return profileShimmer(shimmerSize);
                } else {
                  final pageData = detailController.pageData.value;
                  return Column(
                    children: [
                      topHeaderSection(pageData, size, context),
                      sizedBox(50, 0),
                      pageButton(detailController, widget.pageID, context, size),
                      sizedBox(15, 0),
                      pageAbout(size, pageData),
                      sizedBox(15, 0),
                      newPost(
                        context: context,
                        size: size,
                        pageData: pageData,
                        pageID: widget.pageID,
                        controller: controller,
                        name: pageData.name!,
                        avatar: pageData.avatar!,
                      ),
                      sizedBox(15, 0),

                    ],
                  );
                }
              }),
              pagePostSection(shimmerSize, size, context)
            ],
          ),
        ),
      ),
    );
  }

  Widget pagePostSection(Size shimmerSize, double size, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Posts',
            style: TextStyle(
                color: black, fontSize: 20, fontWeight: FontWeight.w500),
          ),
          Divider(
            color: black.withOpacity(.1),
          ),
          Obx(() {
            if (controller.isLoading.value) {
              return postShimmer(shimmerSize);
            } else {
              return controller.postData.isEmpty
                  ? const Text('No posts found')
                  : ListView.builder(
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      primary: false,
                      itemCount: controller.postData.length,
                      itemBuilder: (_, index) {
                        final data = controller.postData[index];
                        final fileType = path.extension(data.postFile!);
                        return Container(
                          margin: const EdgeInsets.only(bottom: 20),
                          decoration: BoxDecoration(
                              color: white,
                              borderRadius: data.isBoosted == 1
                                  ? const BorderRadius.only(
                                      bottomRight: Radius.circular(20),
                                      bottomLeft: Radius.circular(20),
                                    )
                                  : BorderRadius.circular(20)),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                // publisher start
                                mainPublisher(
                                    data, size, context, postActionController),
                                // publisher end
                                data.postFeeling != ''
                                    ? postFeeling(data)
                                    : Container(),
                                const Divider(),
                                sizedBox(5, 0),
                                data.postText == ''
                                    ? Container()
                                    : Html(data: data.postText!),
                                data.sharedInfo == null
                                    ? Container()
                                    : sharedInfo(data, size),
                                data.multiImage == '1'
                                    ? multiImage(data, size, pageController)
                                    : Container(),
                                data.postFile!.isNotEmpty
                                    ? fileType == '.mp4' ||
                                            fileType == '.m4v' ||
                                            fileType == '.webm' ||
                                            fileType == '.flv' ||
                                            fileType == '.mov' ||
                                            fileType == '.mpeg' ||
                                            fileType == '.mkv'
                                        ? AspectRatio(
                                            aspectRatio: 16 / 9,
                                            child: BetterPlayer.network(
                                              data.postFile!,
                                              betterPlayerConfiguration:
                                                  const BetterPlayerConfiguration(
                                                aspectRatio: 16 / 9,
                                              ),
                                            ),
                                          )
                                        : fileType == '.jpg' ||
                                                fileType == '.jpeg' ||
                                                fileType == '.png' ||
                                                fileType == '.gif'
                                            ? InkWell(
                                  onTap: (){
                                    showDialog(
                                        context: context,
                                        builder: (_) => AlertDialog(
                                          insetPadding: EdgeInsets.zero,
                                          contentPadding: EdgeInsets.zero,
                                          clipBehavior:
                                          Clip.antiAliasWithSaveLayer,
                                          shape: const RoundedRectangleBorder(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(10.0))),
                                          content: Builder(builder: (context) {
                                            return Image.network(
                                              data.postFile!,
                                              fit: BoxFit.cover,
                                            );
                                          }),
                                        ));
                                  },
                                  child: Container(
                                    height: 250,
                                    width: size * .9,
                                    decoration: BoxDecoration(
                                        color: Colors.grey,
                                        borderRadius:
                                        BorderRadius.circular(20),
                                        image: DecorationImage(
                                          image: NetworkImage(
                                              data.postFile!),
                                          fit: BoxFit.cover,
                                        )),
                                  ),
                                )
                                            : Container()
                                    : Container(),
                                sizedBox(10, 0),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    postMeta(data),
                                    const Divider(),
                                    postAction(controller, data, index, context),
                                  ],
                                ),
                                sizedBox(20, 0),
                                data.getPostComments!.isNotEmpty
                                    ? postComment(data, context)
                                    : Container(),
                                data.getPostComments!.isNotEmpty
                                    ? sizedBox(20, 0)
                                    : Container(),
                                data.isComment == "0"
                                    ? commentInactive(size)
                                    : CommentActive(data),
                                sizedBox(10, 0),
                              ],
                            ),
                          ),
                        );
                      });
            }
          })
        ],
      ),
    );
  }
}
