import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/auth/login_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({Key? key}) : super(key: key);

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final passController = TextEditingController();
  final emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool showPassword = true;
  bool isApiCallProcess = false;
  bool? inAsyncCall;
  final _controller = Get.put(LoginController());

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: false,
      backgroundColor: background,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: background,
        automaticallyImplyLeading: false,
        elevation: 0,
        title: Text(
          login,
          style: const TextStyle(
            color: primary,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: size * .9,
              decoration: BoxDecoration(
                color: white,
                boxShadow: [
                  BoxShadow(
                    color: black.withOpacity(0.1),
                    blurRadius: 5,
                  )
                ],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Column(
                  children: [
                    Column(
                      children: [
                        Container(
                          height: 100,
                          width: 100,
                          decoration: BoxDecoration(
                            border: Border.all(color: primary, width: 2),
                            color: background,
                            shape: BoxShape.circle,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Center(
                              child: Lottie.asset(lockJson),
                            ),
                          ),
                        ),
                        sizedBox(20, 0),
                        Text(
                          login,
                          style: const TextStyle(
                            color: black,
                            fontWeight: FontWeight.w600,
                            fontSize: 25,
                          ),
                        ),
                        Text(
                          credentials,
                          style: TextStyle(
                            color: black.withOpacity(0.5),
                            fontWeight: FontWeight.w300,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    sizedBox(30, 0),
                    Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            TextFormField(
                              controller: emailController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return usernameError;
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(Icons.alternate_email),
                                label: Text(username),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(15, 0),
                            TextFormField(
                              controller: passController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return passError;
                                }
                                return null;
                              },
                              obscuringCharacter: secureText,
                              obscureText: showPassword,
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(Icons.lock_open),
                                suffixIcon: InkWell(
                                  onTap: () {
                                    showPassword = !showPassword;
                                    setState(() {});
                                  },
                                  child: showPassword
                                      ? const Icon(Icons.visibility)
                                      : const Icon(Icons.visibility_off),
                                ),
                                label: Text(password),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(20, 0),
                          ],
                        )),
                    Obx(() {
                      if (_controller.isLoading.value) {
                        return const Center(
                            child: CircularProgressIndicator());
                      } else {
                        return InkWell(
                          onTap: () async {
                            if (_formKey.currentState!.validate()) {
                              _controller.userLogin(
                                  emailController.text, passController.text);
                            }
                          },
                          child: CustomButton(title: continueText),
                        );
                      }
                    }),
                    sizedBox(20, 0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            navigate('forgot');
                          },
                          child: const Text(
                            'Forgot Password?',
                            style: TextStyle(
                              color: primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    sizedBox(10, 0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(noAcc),
                        InkWell(
                          onTap: () {

                            navigate('register');
                          },
                          child: Text(
                            registerHere,
                            style: const TextStyle(
                              color: primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            sizedBox(50, 0),
          ],
        ),
      ),
    );
  }
}
