import 'dart:convert';

UserDetailsModel userDetailsModelFromJson(String str) =>
    UserDetailsModel.fromJson(json.decode(str));

String userDetailsModelToJson(UserDetailsModel data) =>
    json.encode(data.toJson());

class UserDetailsModel {
  int? apiStatus;
  UserData? userData;

  UserDetailsModel({
    this.apiStatus,
    this.userData,
  });

  factory UserDetailsModel.fromJson(Map<String, dynamic> json) =>
      UserDetailsModel(
        apiStatus: json["api_status"],
        userData: json["user_data"] == null
            ? null
            : UserData.fromJson(json["user_data"]),
      );

  Map<String, dynamic> toJson() => {
        "api_status": apiStatus,
        "user_data": userData?.toJson(),
      };
}

class UserData {
  String? userId;
  String? name;
  String? url;
  String? firstName;
  String? lastName;
  String? userName;
  String? avatar;
  String? cover;
  String? working;
  String? isVerified;
  String? isPro;
  String? followerCount;
  String? followingCount;
  String? isAdmin;
  String? address;
  String? relationshipID;
  String? birthday;
  String? phnNumber;
  String? email;
  String? gender;
  String? school;
  String? schoolCompleted;
  String? companyWebsite;
  String? facebook;
  String? google;
  String? twitter;
  String? linkedin;
  String? youtube;
  String? vk;
  String? instagram;
  String? about;
  String? followPrivacy;
  String? friendPrivacy;
  String? postPrivacy;
  String? messagePrivacy;
  String? confirmFollowers;
  String? showActivitiesPrivacy;
  String? birthPrivacy;
  String? visitPrivacy;
  String? shareMyLocation;
  String? status;
  int? isFollowing;

  UserData({
    this.email,
    this.userId,
    this.userName,
    this.url,
    this.name,
    this.firstName,
    this.lastName,
    this.cover,
    this.avatar,
    this.working,
    this.isVerified,
    this.isPro,
    this.followerCount,
    this.isFollowing,
    this.followingCount,
    this.isAdmin,
    this.address,
    this.relationshipID,
    this.birthday,
    this.phnNumber,
    this.gender,
    this.school,
    this.schoolCompleted,
    this.companyWebsite,
    this.facebook,
    this.google,
    this.twitter,
    this.linkedin,
    this.youtube,
    this.vk,
    this.about,
    this.instagram,
    this.followPrivacy,
    this.friendPrivacy,
    this.postPrivacy,
    this.messagePrivacy,
    this.confirmFollowers,
    this.showActivitiesPrivacy,
    this.birthPrivacy,
    this.visitPrivacy,
    this.shareMyLocation,
    this.status,
  });

  void setIsLiked(int value) {
    isFollowing = value;
  }

  factory UserData.fromJson(Map<String, dynamic> json) => UserData(
        userId: json["user_id"],
        firstName: json['first_name'],
        lastName: json['last_name'],
        url: json['url'],
        email: json['email'],
        isFollowing: json['is_following'],
        name: json["name"],
        isPro: json['is_pro'],
        userName: json['username'],
        cover: json["cover"],
        avatar: json["avatar"],
        working: json["working"],
        isVerified: json['verified'],
        followerCount: json['details']['followers_count'],
        followingCount: json['details']['following_count'],
        isAdmin: json['admin'],
        address: json['address'],
        relationshipID: json['relationship_id'],
        birthday: json['birthday'],
        phnNumber: json['phone_number'],
        gender: json['gender'],
        school: json['school'],
        schoolCompleted: json['school_completed'],
        companyWebsite: json['working_link'],
        facebook: json["facebook"],
        google: json["google"],
        twitter: json["twitter"],
        linkedin: json["linkedin"],
        youtube: json["youtube"],
        vk: json["vk"],
        instagram: json["instagram"],
        about: json['about'],
        followPrivacy: json["follow_privacy"],
        friendPrivacy: json["friend_privacy"],
        postPrivacy: json["post_privacy"],
        messagePrivacy: json["message_privacy"],
        confirmFollowers: json["confirm_followers"],
        showActivitiesPrivacy: json["show_activities_privacy"],
        birthPrivacy: json["birth_privacy"],
        visitPrivacy: json["visit_privacy"],
        shareMyLocation: json["share_my_location"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "name": name,
        "cover": cover,
        "avatar": avatar,
        "working": working
      };
}
