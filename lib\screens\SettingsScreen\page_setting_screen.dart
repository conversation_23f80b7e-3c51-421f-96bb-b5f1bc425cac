import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Page/Setting/page_information_setting.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import '../../components/Page/Setting/about_setting.dart';
import '../../components/Page/Setting/general_setting.dart';
import '../../components/Page/Setting/networking_setting.dart';
import '../../components/Settings/settings_items.dart';
import '../../utils/colors.dart';

class PageSetting extends StatefulWidget {
  final String pageID;

  const PageSetting({Key? key, required this.pageID}) : super(key: key);

  @override
  State<PageSetting> createState() => _PageSettingState();
}

class _PageSettingState extends State<PageSetting> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Page Setting'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            sizedBox(10, 0),
            Divider(
              color: black.withOpacity(.1),
            ),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(
                      context,
                      PageGeneralSetting(
                        pageID: widget.pageID,
                      ));
                },
                size: size,
                title: 'General Information',
                icon: 'assets/svg/person.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(
                      context,
                      PageInformation(
                        pageID: widget.pageID,
                      ));
                },
                size: size,
                title: 'Page Information',
                icon: 'assets/svg/infoo.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(
                      context,
                      PageNetworkingSetting(
                        pageID: widget.pageID,
                      ));
                },
                size: size,
                title: 'Networking',
                icon: 'assets/svg/network.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(
                      context,
                      PageAboutSetting(
                        pageID: widget.pageID,
                      ));
                },
                size: size,
                title: 'About',
                icon: 'assets/svg/aboutSection.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  APISERvices.pageVerification(pageID: widget.pageID);
                },
                size: size,
                title: 'Request for verification',
                icon: 'assets/svg/verify.svg'),
          ],
        ),
      ),
    );
  }
}
