import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/Search/search_controller.dart';
import '../../utils/colors.dart';
import '../../widgets/Search/group_result.dart';
import '../../widgets/Search/page_result.dart';
import '../../widgets/Search/user_result.dart';

class SearchResultScreen extends StatefulWidget {
  final String? userInput;

  const SearchResultScreen({Key? key, this.userInput}) : super(key: key);

  @override
  State<SearchResultScreen> createState() => _SearchResultScreenState();
}

class _SearchResultScreenState extends State<SearchResultScreen>
    with TickerProviderStateMixin {
  TabController? tabController;
  final controller = Get.put(SearchResultsController());

  @override
  void initState() {
    super.initState();
    tabController = TabController(
      initialIndex: 0,
      length: 3,
      vsync: this,
    );
    controller.getData(searchText: widget.userInput);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: background,
      appBar: AppBar(
        leading: IconButton(
            onPressed: () {
              Get.back();
            }, //
            icon: const Icon(
              Icons.arrow_back_ios_rounded,
              color: white,
            )),
        backgroundColor: black.withOpacity(0.5),
        automaticallyImplyLeading: false,
        elevation: 0,
        title: const Text(
          'Search Result',
          style: TextStyle(
            color: white,
            fontWeight: FontWeight.w400,
          ),
        ),
        bottom: TabBar(
          isScrollable: false,
          indicatorColor: primary,
          controller: tabController,
          tabs: [
            Tab(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Icon(
                    Icons.person_sharp,
                    color: white,
                  ),
                  Text(
                    'Users',
                    style: TextStyle(color: white),
                  )
                ],
              ),
            ),
            Tab(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Icon(
                    Icons.table_chart_sharp,
                    color: white,
                  ),
                  Text(
                    'Pages',
                    style: TextStyle(color: white),
                  )
                ],
              ),
            ),
            Tab(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: const [
                  Icon(
                    Icons.group,
                    color: white,
                  ),
                  Text(
                    'Groups',
                    style: TextStyle(color: white),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: tabController,
          children: const [
            UserSearchResults(),
            PageSearchResults(),
            GroupSearchResults()
      ]),
    );
  }
}
