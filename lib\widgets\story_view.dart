// ignore_for_file: unused_local_variable

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:like_button/like_button.dart';
import 'package:story/story_page_view.dart';
import 'package:get/get.dart';

import '../controllers/Story/get_all_stories.dart';
import '../services/api_services.dart';
import '../utils/colors.dart';
import '../utils/config.dart';

class StoryViewScreen extends StatefulWidget {
  final int pageIndex;

  const StoryViewScreen({Key? key, required this.pageIndex}) : super(key: key);

  @override
  State<StoryViewScreen> createState() => _StoryViewScreenState();
}

class _StoryViewScreenState extends State<StoryViewScreen>
    with TickerProviderStateMixin {
  final allStory = Get.put(GetAllStoriesController());
  String storyID = '';
  bool? isOwner;
  final isLiked = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: black,
      body: Column(
        children: [
          Expanded(
            child: StoryPageView(
              indicatorDuration: Duration(seconds: storyDuration),
              initialPage: widget.pageIndex,
              itemBuilder: (context, pageIndex, storyIndex) {
                final user = allStory.getAllStories['stories'][pageIndex];
                final story = user['stories'][storyIndex];
                storyID = allStory.getAllStories['stories'][pageIndex]
                    ['stories'][storyIndex]['id'];
                isOwner = allStory.getAllStories['stories'][pageIndex]
                    ['stories'][storyIndex]['is_owner'];
                return Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Positioned.fill(
                      child: Container(color: Colors.black),
                    ),
                    Positioned.fill(
                      child: Image.network(
                        story['thumbnail'],
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 44, left: 8),
                      child: Row(
                        children: [
                          Container(
                            height: 32,
                            width: 32,
                            decoration: BoxDecoration(
                              image: DecorationImage(
                                image:
                                    NetworkImage(story['user_data']['avatar']),
                                fit: BoxFit.cover,
                              ),
                              shape: BoxShape.circle,
                            ),
                          ),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(
                            story['user_data']['name'],
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
              gestureItemBuilder: (context, pageIndex, storyIndex) {
                return Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 32),
                    child: isOwner!
                        ? Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                padding: EdgeInsets.zero,
                                color: Colors.white,
                                icon: const Icon(Icons.delete),
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                              ),
                              IconButton(
                                padding: EdgeInsets.zero,
                                color: Colors.white,
                                icon: const Icon(Icons.close),
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                              ),
                            ],
                          )
                        : IconButton(
                            padding: EdgeInsets.zero,
                            color: Colors.white,
                            icon: const Icon(Icons.close),
                            onPressed: () {
                              Navigator.pop(context);
                            },
                          ),
                  ),
                );
              },
              pageLength: allStory.getAllStories['stories'].length,
              storyLength: (int pageIndex) {
                return allStory
                    .getAllStories['stories'][pageIndex]['stories'].length;
              },
              onPageLimitReached: () {
                Navigator.pop(context);
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/like.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });

                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '1');
                    return !isLiked;
                  },
                ),
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/love.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });

                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '2');
                    return !isLiked;
                  },
                ),
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/haha.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });

                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '3');
                    return !isLiked;
                  },
                ),
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/wow.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });
                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '4');
                    return !isLiked;
                  },
                ),
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/sad.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });
                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '5');
                    return !isLiked;
                  },
                ),
                LikeButton(
                  isLiked: isLiked,
                  size: 40,
                  likeBuilder: (isTapped) {
                    return Image.asset(
                      'assets/gif/angry.gif',
                      width: 35,
                    );
                  },
                  onTap: (isLiked) async {
                    AudioPlayer().play(AssetSource('audios/reacted.mp3'));
                    setState(() {
                      isLiked = false;
                    });
                    final success = await APISERvices.storyReaction(
                        storyID: storyID, reactType: '6');
                    return !isLiked;
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
