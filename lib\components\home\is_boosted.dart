import 'package:flutter/material.dart';

import '../../utils/colors.dart';

Widget boostedBadge(size) {
  return Container(
    width: size,
    decoration: const BoxDecoration(
      color: primary,
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    child: const Padding(
      padding: EdgeInsets.all(8.0),
      child: Center(
        child: Text(
          'Sponsored',
          style: TextStyle(
            color: white,
          ),
        ),
      ),
    ),
  );
}
