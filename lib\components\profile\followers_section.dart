import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_followers_controller.dart';
import 'package:get/get.dart';

import '../../utils/colors.dart';

class FollowerSection extends StatefulWidget {
  final String userID;
  const FollowerSection({Key? key, required this.userID}) : super(key: key);

  @override
  State<FollowerSection> createState() => _FollowerSectionState();
}

class _FollowerSectionState extends State<FollowerSection> {
  final controller = Get.put(GetUserFollowersController());
  @override
  void initState() {
    controller.getData(userID: widget.userID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    return Obx(() {
      if(controller.isLoading.value){
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
            child: storyShimmer());
      } else{
        return controller.followersData.value.followers == null
          ? storyShimmer()
          : SizedBox(
          height: 100,
          child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: controller.followersData.value.followers!.length,
              shrinkWrap: true,
              primary: false,
              itemBuilder: (_, index) {
                var data = controller.followersData.value.followers![index];
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      Container(
                        height: 60,
                        width: 70,
                        decoration: BoxDecoration(
                            image: DecorationImage(
                                image: NetworkImage(data.avatar!),
                                fit: BoxFit.cover),
                            color: primary,
                            shape: BoxShape.circle),
                      ),
                      Text(data.name!,
                        style:
                        TextStyle(color: black.withOpacity(0.4), fontSize: 12),
                      )
                    ],
                  ),
                );
              }),
        );
      }
    });
  }
}
