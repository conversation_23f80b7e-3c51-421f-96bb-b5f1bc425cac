import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:flutter_wowonder/widgets/form_helper.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../utils/colors.dart';

class GeneralSetting extends StatefulWidget {
  const GeneralSetting({Key? key}) : super(key: key);

  @override
  State<GeneralSetting> createState() => _GeneralSettingState();
}

enum SingingCharacter { male, female }

class _GeneralSettingState extends State<GeneralSetting> {
  final userController = Get.put(MyDataController());

  final firstNameCtrl = TextEditingController();
  final lastNameCtrl = TextEditingController();
  final dobCtrl = TextEditingController();
  final phnCtrl = TextEditingController();
  final emailCtrl = TextEditingController();
  String gender = 'male';
  SingingCharacter? genderList = SingingCharacter.male;
  File? newImage;
  Future pickImage() async {
    try {
      final image =
      await ImagePicker().pickImage(source: ImageSource.gallery);
      if (image == null) return;
      File? img = File(image.path);
      setState(() {
        newImage = img;
      });
    } on PlatformException {
      return null;
    }
  }

  changeData () {
    final data = userController.getUserDetails.value;
    firstNameCtrl.text = data.firstName!;
    lastNameCtrl.text = data.lastName!;
    dobCtrl.text = data.birthday!;
    phnCtrl.text = data.phnNumber!;
    emailCtrl.text = data.email!;
  }
  @override
  void initState() {
    changeData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final userData = userController.getUserDetails.value;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'General'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sizedBox(10, 0),
              InkWell(
                onTap: (){
                  pickImage();
                },
                child: Center(
                  child: Container(
                    height: 80,
                    width: 80,
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: white,
                        image: DecorationImage(
                          image: NetworkImage(userData.avatar!),
                        )),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                            decoration: const BoxDecoration(
                              color: primary,
                              shape: BoxShape.circle,
                            ),
                            child: const Padding(
                              padding: EdgeInsets.all(3.0),
                              child: Icon(
                                Icons.edit,
                                color: white,
                                size: 15,
                              ),
                            ))
                      ],
                    ),
                  ),
                ),
              ),
              sizedBox(20, 0),
              const Text(
                'Basic Details',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              sizedBox(10, 0),
              customFieldHelper(
                  controller: firstNameCtrl,
                  prefixIcon: Icons.person,
                  label: 'First Name'),
              sizedBox(15, 0),
              customFieldHelper(
                  controller: lastNameCtrl,
                  prefixIcon: Icons.person,
                  label: 'Last Name'),
              sizedBox(15, 0),
              customFieldHelper(
                  controller: dobCtrl,
                  prefixIcon: Icons.date_range,
                  label: 'Date of birth',
                  hintText: 'Format xxxx-xx-xx'),
              sizedBox(15, 0),
              customFieldHelper(
                  controller: phnCtrl,
                  prefixIcon: Icons.call,
                  label: 'Phone Number'),
              sizedBox(15, 0),
              customFieldHelper(
                  controller: emailCtrl,
                  prefixIcon: Icons.alternate_email,
                  label: 'Email address'),
              sizedBox(15, 0),
              const Text('Gender'),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        genderList = SingingCharacter.male;
                        gender = 'male';
                        setState(() {});
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(color: black.withOpacity(.1))),
                        child: Row(
                          children: [
                            Radio<SingingCharacter>(
                              value: SingingCharacter.male,
                              groupValue: genderList,
                              onChanged: (SingingCharacter? value) {
                                setState(() {
                                  genderList = value;
                                  gender = 'male';
                                });
                              },
                            ),
                            const Text('Male'),
                            sizedBox(0, 10),
                          ],
                        ),
                      ),
                    ),
                  ),
                  sizedBox(0, 10),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        genderList = SingingCharacter.female;
                        gender = 'female';
                        setState(() {});
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(color: black.withOpacity(.1))),
                        child: Row(
                          children: [
                            Radio<SingingCharacter>(
                              value: SingingCharacter.female,
                              groupValue: genderList,
                              onChanged: (SingingCharacter? value) {
                                setState(() {
                                  genderList = value;
                                  gender = 'female';
                                });
                              },
                            ),
                            const Text('Female'),
                            sizedBox(0, 10),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              sizedBox(15, 0),
              CustomButton(
                title: 'Save',
                onTap: () {
                  APISERvices.updateUserData(
                    firstName: firstNameCtrl.text.isEmpty
                        ? userData.firstName
                        : firstNameCtrl.text.toString(),
                    lastName: lastNameCtrl.text.isEmpty
                        ? userData.lastName
                        : lastNameCtrl.text.toString(),
                    email: emailCtrl.text.isEmpty
                        ? userData.email
                        : emailCtrl.text.toString(),
                    phoneNumber: phnCtrl.text.isEmpty
                        ? userData.phnNumber
                        : phnCtrl.text.toString(),
                    birthday: dobCtrl.text.isEmpty
                        ? userData.birthday
                        : dobCtrl.text.toString(),
                    gender: gender,
                  );
                  UserDataController().refreshData();
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
