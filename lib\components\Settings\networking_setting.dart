import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Settings/social_items.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import '../../services/api_services.dart';
import '../../widgets/button_widget.dart';
import 'package:get/get.dart';

class NetworkingSetting extends StatefulWidget {
  const NetworkingSetting({Key? key}) : super(key: key);

  @override
  State<NetworkingSetting> createState() => _NetworkingSettingState();
}

class _NetworkingSettingState extends State<NetworkingSetting> {
  final facebook = TextEditingController();
  final twitter = TextEditingController();
  final vk = TextEditingController();
  final linkedin = TextEditingController();
  final instagram = TextEditingController();
  final youtube = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final profile = Get.put(UserDataController());
    final data = profile.getUserDetails.value;
    final size = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Networking'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
          vertical: 10,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              socialItems(
                controller: facebook,
                size: size,
                title: 'Facebook',
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Twitter',
                controller: twitter,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Vkontakte',
                controller: vk,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Linkedin',
                controller: linkedin,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Instagram',
                controller: instagram,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'YouTube',
                controller: youtube,
              ),
              sizedBox(15, 0),
              CustomButton(
                title: 'Save',
                onTap: () {
                  APISERvices.updateSocial(
                    context: context,
                    facebook: facebook.text.isEmpty ? data.facebook : facebook.text.toString(),
                    twitter: twitter.text.isEmpty ? data.twitter : twitter.text.toString(),
                    vk: vk.text.isEmpty ? data.vk : vk.text.toString(),
                    instagram: instagram.text.isEmpty ? data.instagram : instagram.text.toString(),
                    linkedin: linkedin.text.isEmpty ? data.linkedin : linkedin.text.toString(),
                    youtube: youtube.text.isEmpty ? data.youtube : youtube.text.toString(),
                  );
                  UserDataController().refreshData;
                  UserDataController().refreshData;
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
