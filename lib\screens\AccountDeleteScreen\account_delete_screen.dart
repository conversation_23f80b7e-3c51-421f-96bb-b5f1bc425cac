import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:lottie/lottie.dart';
import 'package:get/get.dart';
import '../../services/api_services.dart';
import '../../widgets/button_widget.dart';

class AccountDeleteScreen extends StatelessWidget {
  const AccountDeleteScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final globalKey = GlobalKey<FormState>();
    final passController = TextEditingController();
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Delete Account'),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Lottie.asset(
              'assets/json/delete.json',
              width: size * .5,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Text(
                'After delete your account you can\'t able to access you data anymore and the data will be delete from our server.',
                textAlign: TextAlign.center,
                style: TextStyle(color: black.withOpacity(.5), fontSize: 18),
              ),
            ),
            sizedBox(20, 0),
            CustomButton(
              title: 'Delete Account',
              onTap: () {
                Get.defaultDialog(
                  title: 'Confirm Password',
                  textConfirm: 'Delete',
                  textCancel: 'Cancel',
                  cancelTextColor: primary,
                  confirmTextColor: white,
                  onConfirm: () {
                    if (globalKey.currentState!.validate()) {
                      APISERvices.deleteAccount(
                        password: passController.text,
                        context: context,
                      );
                    }
                  },
                  content: Container(
                    decoration: BoxDecoration(
                      color: black.withOpacity(.1),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10.0),
                      child: Form(
                        key: globalKey,
                        child: TextFormField(
                          controller: passController,
                          validator: (value) {
                            if (value!.isEmpty) {
                              return 'Please enter your account password';
                            }
                            return null;
                          },
                          decoration:
                              const InputDecoration(border: InputBorder.none),
                        ),
                      ),
                    ),
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
