import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedService {
  static Future<bool> isLoggedIn() async {
    final pref = await SharedPreferences.getInstance();
    return pref.getString('token') != null ? true : false;
  }

  static void logOut() async {
    final pref = await SharedPreferences.getInstance();
    Get.defaultDialog(
      title: 'Logout',
      content: const Text('Are you sure?'),
      confirm: TextButton(
          onPressed: () {
            pref.remove('token');
            navigate('login');
          },
          child: const Text('Yes')),
      cancel: TextButton(
          onPressed: () {
            Get.back();
          },
          child: const Text('No')),
    );
  }
}
