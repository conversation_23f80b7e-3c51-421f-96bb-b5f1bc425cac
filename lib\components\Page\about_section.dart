import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../models/Pages/pages_details_model.dart';
import '../../utils/colors.dart';
Widget pageAbout(double size, PageData pageData) {
  return Center(
    child: Container(
      width: size * .9,
      decoration: BoxDecoration(
        color: white,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15,
          vertical: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('About',
              style: TextStyle(
                  color: black.withOpacity(.3)
              ),),
            Divider(
              color: black.withOpacity(.1),
            ),
            Text(pageData.about!),
            Divider(
              color: black.withOpacity(.1),
            ),
            pageData.facebook!.isEmpty
                ? Container()
                : Row(
              children: [
                Icon(
                  Icons.facebook,
                  size: 15,
                  color: black.withOpacity(.5),
                ),
                Text(pageData.facebook!)
              ],
            ),
            pageData.twitter!.isEmpty
                ? Container()
                : Row(
              children: [
                SvgPicture.asset(
                  'assets/svg/twitter.svg',
                  // ignore: deprecated_member_use
                  color: black.withOpacity(.5),
                  width: 14,
                ),
                Text(pageData.twitter!)
              ],
            )
          ],
        ),
      ),
    ),
  );
}