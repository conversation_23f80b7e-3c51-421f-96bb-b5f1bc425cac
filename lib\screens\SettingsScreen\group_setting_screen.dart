import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Groups/Setting/general_setting.dart';
import 'package:flutter_wowonder/components/Groups/Setting/privacy_setting.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/GroupsScreen/member_request_screen.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import '../../components/Settings/settings_items.dart';
import '../../utils/colors.dart';

class GroupSettingScreen extends StatefulWidget {
  final String groupID;
  const GroupSettingScreen({Key? key, required this.groupID}) : super(key: key);

  @override
  State<GroupSettingScreen> createState() => _GroupSettingScreenState();
}

class _GroupSettingScreenState extends State<GroupSettingScreen> {
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Group Setting'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            sizedBox(10, 0),
            Divider(
              color: black.withOpacity(.1),
            ),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(context, GroupGeneralSetting(groupID: widget.groupID));
                },
                size: size,
                title: 'General Information',
                icon: 'assets/svg/person.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(context, GroupPrivacy(groupID: widget.groupID));
                },
                size: size,
                title: 'Privacy',
                icon: 'assets/svg/privacySetting.svg'),
            sizedBox(10, 0),
            settingItem(
                onTap: () {
                  pageRoute(context, MemberRequestScreen(groupID: widget.groupID));
                },
                size: size,
                title: 'Member Request',
                icon: 'assets/svg/aboutSection.svg'),
            sizedBox(10, 0),
          ],
        ),
      ),
    );
  }
}
