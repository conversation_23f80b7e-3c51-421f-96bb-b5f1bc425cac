// ignore_for_file: unnecessary_null_comparison, deprecated_member_use, invalid_use_of_protected_member

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/controllers/Story/get_all_stories.dart';
import 'package:flutter_wowonder/widgets/story_view.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:story_creator/story_creator.dart';
import '../components/Shimmer/shimmer_effect.dart';
import '../services/api_services.dart';
import '../utils/colors.dart';

class UserStory extends StatefulWidget {
  const UserStory({Key? key}) : super(key: key);

  @override
  State<UserStory> createState() => _UserStoryState();
}

class _UserStoryState extends State<UserStory> {
  final controller = Get.put(GetAllStoriesController());
  File? image2;

  Future<File?> pickImage() async {
    try {
      final image = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (image == null) return null;
      File? img = File(image.path);
      setState(() {
        image2 = img;
      });
      return img;
    } on PlatformException {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    Get.put(GetAllStoriesController());
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            InkWell(
              onTap: () async {
                pickImage().then((value) async {
                  File editedFile = await Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => StoryCreator(
                        filePath: value!.path,
                      ),
                    ),
                  );
                  if (editedFile != null) {
                    APISERvices.createStory(
                        filePath: editedFile.path,
                        filesType: 'image',
                        storyTitle: 'Test');
                  } else {}
                });
              },
              child: Container(
                height: 70,
                width: 70,
                decoration: const BoxDecoration(
                  color: primary,
                  shape: BoxShape.circle,
                ),
                child: Center(
                    child: SvgPicture.asset(
                  'assets/svg/camera.svg',
                  color: white,
                )),
              ),
            ),
            Obx(() {
              if (controller.isLoading.value) {
                return storyShimmer();
              } else {
                return SizedBox(
                  height: 90,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: ListView.builder(
                        shrinkWrap: true,
                        primary: false,
                        itemCount:
                            controller.getAllStories.value['stories'].length,
                        scrollDirection: Axis.horizontal,
                        itemBuilder: (_, index) {
                          final data =
                              controller.getAllStories.value['stories'][index];
                          return InkWell(
                            onTap: () {
                              pageRoute(
                                  context,
                                  StoryViewScreen(
                                    pageIndex: index,
                                  ));
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: primary,
                                  width: 2,
                                ),
                                shape: BoxShape.circle,
                              ),
                              child: Container(
                                decoration: const BoxDecoration(
                                    shape: BoxShape.circle, color: white),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: SizedBox(
                                    width: 70,
                                    child: CircleAvatar(
                                      backgroundImage:
                                          NetworkImage(data['avatar']),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        }),
                  ),
                );
              }
            }),
          ],
        ),
      ),
    );
  }
}
