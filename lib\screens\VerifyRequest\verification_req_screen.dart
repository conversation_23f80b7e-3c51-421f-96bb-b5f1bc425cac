import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/screens/VerifyRequest/upload_doc_screen.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../components/size_box.dart';
import '../../widgets/button_widget.dart';
import '../../widgets/form_helper.dart';

class VerifyRequestScreen extends StatefulWidget {
  const VerifyRequestScreen({Key? key}) : super(key: key);

  @override
  State<VerifyRequestScreen> createState() => _VerifyRequestScreenState();
}

class _VerifyRequestScreenState extends State<VerifyRequestScreen> {
  final formKey = GlobalKey<FormState>();
  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phnController = TextEditingController();
  final dobController = TextEditingController();
  final profileController = Get.put(MyDataController());

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    final data = profileController.getUserDetails.value;
    return Scaffold(
      backgroundColor: background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: background,
        title: Text(
          'Verification Request',
          style: TextStyle(
              color: black.withOpacity(.5),
              fontSize: 17,
              fontWeight: FontWeight.w400),
        ),
        centerTitle: true,
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              Icons.arrow_back_ios_new,
              color: black.withOpacity(.5),
            )),
      ),
      body: data.isVerified == '1'
          ? Center(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Lottie.asset('assets/json/success.json'),
                  const Text(
                    'Congratulations',
                    style: TextStyle(
                        color: primary,
                        fontWeight: FontWeight.w600,
                        fontSize: 35),
                  ),
                  const Text('You are already earned verified badge.'),
                  sizedBox(20, 0),
                  CustomButton(
                    title: 'Sounds Good!',
                    onTap: () {
                      Get.back();
                    },
                  ),
                ],
              ),
            )
          : Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        width: size * .9,
                        decoration: BoxDecoration(
                          color: white,
                          boxShadow: [
                            BoxShadow(
                              color: black.withOpacity(0.1),
                              blurRadius: 5,
                            )
                          ],
                          borderRadius: BorderRadius.circular(15),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(20.0),
                          child: Column(
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(.1),
                                    shape: BoxShape.circle),
                                child: Lottie.asset(
                                  'assets/json/idintity.json',
                                  width: size * .4,
                                ),
                              ),
                              sizedBox(20, 0),
                              Form(
                                  key: formKey,
                                  child: Column(
                                    children: [
                                      customFieldHelper(
                                          controller: nameController,
                                          label: 'Full Name',
                                          prefixIcon: Icons.person,
                                          validator: (value) {
                                            if (value!.isEmpty) {
                                              return nameError;
                                            }
                                            return null;
                                          }),
                                      sizedBox(15, 0),
                                      customFieldHelper(
                                          controller: emailController,
                                          label: 'Email Address',
                                          prefixIcon: Icons.alternate_email,
                                          validator: (value) {
                                            if (value!.isEmpty) {
                                              return emailError;
                                            }
                                            return null;
                                          }),
                                      sizedBox(15, 0),
                                      customFieldHelper(
                                          controller: phnController,
                                          type: TextInputType.phone,
                                          label: 'Mobile Number',
                                          prefixIcon: Icons.call,
                                          validator: (value) {
                                            if (value!.isEmpty) {
                                              return phnError;
                                            }
                                            return null;
                                          }),
                                      sizedBox(15, 0),
                                      customFieldHelper(
                                          controller: dobController,
                                          type: TextInputType.phone,
                                          hintText: 'Format: 0000-00-00',
                                          label: 'Date Of Birth',
                                          prefixIcon: Icons.date_range,
                                          validator: (value) {
                                            if (value!.isEmpty) {
                                              return dobError;
                                            }
                                            return null;
                                          }),
                                      sizedBox(15, 0),
                                      CustomButton(
                                        onTap: () {
                                          if (formKey.currentState!
                                              .validate()) {
                                            if (nameController.text !=
                                                data.name) {
                                              Get.snackbar('Name Not Match',
                                                  'Your entered name is not matched with your profile name.');
                                            } else if (dobController.text !=
                                                data.birthday) {
                                              Get.snackbar('Birthday Not Match',
                                                  'Your entered birthday is not matched with your profile.');
                                            } else {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (_) =>
                                                          UploadDocScreen(
                                                            name: nameController
                                                                .text
                                                                .toString(),
                                                            email:
                                                                emailController
                                                                    .text
                                                                    .toString(),
                                                            phoneNumber:
                                                                phnController
                                                                    .text
                                                                    .toString(),
                                                            dateOfBirth:
                                                                dobController
                                                                    .text
                                                                    .toString(),
                                                          )));
                                            }
                                          }
                                        },
                                        title: 'Continue',
                                      ),
                                    ],
                                  ))
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
    );
  }
}
