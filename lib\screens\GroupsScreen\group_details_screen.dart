import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/Groups/group_details_controller.dart';
import 'package:flutter_wowonder/screens/SettingsScreen/group_setting_screen.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:flutter_wowonder/widgets/group_post_data.dart';
import 'package:flutter_wowonder/widgets/inside_page_appbar.dart';
import '../../components/Groups/new_post.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';
import 'package:get/get.dart';

class GroupDetailsScreen extends StatefulWidget {
  final String groupID;

  const GroupDetailsScreen({Key? key, required this.groupID}) : super(key: key);

  @override
  State<GroupDetailsScreen> createState() => _GroupDetailsScreenState();
}

class _GroupDetailsScreenState extends State<GroupDetailsScreen> {
  final controller = Get.put(GroupDetailsController());
  final formKey = GlobalKey<FormState>();
  final reportTextController = TextEditingController();

  @override
  void initState() {
    controller.getData(pageID: widget.groupID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    final shimmerSize = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      extendBodyBehindAppBar: true,
      appBar: insidePageAppBar(
          size: size,
          title: '',
          context: context,
          menuItem: [
            const PopupMenuItem(value: 0, child: Text('Report Group')),
            const PopupMenuItem(value: 1, child: Text('Copy link')),
          ],
          onSelected: (value) {
            switch (value) {
              case 0:
                Get.defaultDialog(
                    title: 'Report',
                    content: Form(
                      key: formKey,
                      child: Container(
                        width: size * .9,
                        decoration: BoxDecoration(
                            color: Colors.grey.withValues(alpha: .1),
                            borderRadius: BorderRadius.circular(20)),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15.0),
                          child: Center(
                            child: TextFormField(
                              validator: (value) {
                                if (value!.isEmpty) {
                                  return 'Please write something wrong about this page';
                                }
                                return null;
                              },
                              keyboardType: TextInputType.multiline,
                              minLines: 2,
                              maxLines: 5,
                              controller: reportTextController,
                              decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  hintText: 'What\'s wrong with this page?'),
                            ),
                          ),
                        ),
                      ),
                    ),
                    textCancel: 'Cancel',
                    textConfirm: 'Send Report',
                    cancelTextColor: primary,
                    confirmTextColor: white,
                    onCancel: () {
                      reportTextController.clear();
                    },
                    onConfirm: () {
                      if (formKey.currentState!.validate()) {
                        APISERvices.reportGroup(
                          groupID: widget.groupID,
                          text: reportTextController.text.toString(),
                        );
                        reportTextController.clear();
                        Navigator.pop(context);
                        Get.snackbar(
                          'Report Sent',
                          'Your report has been sent to our team successfully.',
                        );
                      } else {
                        return;
                      }
                    });
                break;
              case 1:
                Clipboard.setData(
                    ClipboardData(text: controller.groupData.value.url!));
                Get.snackbar('Copied', 'Page link copied successfully!');
            }
          }),
      body: Obx(() {
        if (controller.isLoading.value) {
          return profileShimmer(shimmerSize);
        } else {
          final groupData = controller.groupData.value;
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (_) => AlertDialog(
                              insetPadding: EdgeInsets.zero,
                              contentPadding: EdgeInsets.zero,
                              clipBehavior: Clip.antiAliasWithSaveLayer,
                              shape: const RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10.0))),
                              content: Builder(builder: (context) {
                                return Image.network(
                                  groupData.cover ==
                                      '$siteUrl/upload/photos/d-cover.jpg  ' ? '$siteUrl/upload/photos/d-cover.jpg' : groupData.cover!,
                                  fit: BoxFit.cover,
                                );
                              }),
                            ));
                  },
                  child: Container(
                    height: 180,
                    width: size,
                    decoration: BoxDecoration(
                        color: white,
                        image: DecorationImage(
                          image: groupData.cover ==
                                  '$siteUrl/upload/photos/d-cover.jpg  '
                              ? NetworkImage(
                                  '$siteUrl/upload/photos/d-cover.jpg')
                              : NetworkImage(groupData.cover!),
                          fit: BoxFit.cover,
                        )),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: white,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15.0, vertical: 15),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            InkWell(
                              onTap: (){
                                showDialog(
                                    context: context,
                                    builder: (_) => AlertDialog(
                                      insetPadding: EdgeInsets.zero,
                                      contentPadding: EdgeInsets.zero,
                                      clipBehavior:
                                      Clip.antiAliasWithSaveLayer,
                                      shape: const RoundedRectangleBorder(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(10.0))),
                                      content: Builder(builder: (context) {
                                        return Image.network(
                                          groupData.avatar ==
                                              '$siteUrl/upload/photos/d-group.jpg ' ? '$siteUrl/upload/photos/d-group.jpg': groupData.avatar!,
                                          fit: BoxFit.cover,
                                        );
                                      }),
                                    ));
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                        color: black.withValues(alpha: 0.1), width: 2)),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: CircleAvatar(
                                    radius: 35,
                                    backgroundImage: groupData.avatar ==
                                            '$siteUrl/upload/photos/d-group.jpg '
                                        ? NetworkImage(
                                            '$siteUrl/upload/photos/d-group.jpg')
                                        : NetworkImage(groupData.avatar!),
                                  ),
                                ),
                              ),
                            ),
                            sizedBox(0, 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  groupData.name!,
                                  style: TextStyle(
                                      color: black.withValues(alpha: .8),
                                      fontWeight: FontWeight.bold,
                                      fontSize: 15),
                                ),
                                Text(
                                  groupData.category!,
                                  style: TextStyle(
                                      color: black.withValues(alpha: .5),
                                      fontSize: 13),
                                )
                              ],
                            ),
                          ],
                        ),
                        sizedBox(10, 0),
                        if (groupData.isOwner!)
                          Center(
                            child: InkWell(
                              onTap: () {
                                pageRoute(
                                    context,
                                    GroupSettingScreen(
                                        groupID: groupData.groupId!));
                              },
                              child: Container(
                                width: size * .9,
                                decoration: BoxDecoration(
                                  color: primary,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 15),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      const Icon(
                                        Icons.settings_sharp,
                                        color: white,
                                      ),
                                      sizedBox(0, 5),
                                      const Text(
                                        'Manage Group',
                                        style: TextStyle(color: white),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        else
                          Center(
                            child: InkWell(
                              onTap: () {
                                if (groupData.isGroupJoined == 1) {
                                  controller.statusChange(
                                      widget.groupID, 0, groupData);
                                } else {
                                  controller.statusChange(
                                      widget.groupID, 1, groupData);
                                  groupData.privacy == '1'
                                      ? Container()
                                      : Get.defaultDialog(
                                          title: 'Request sent',
                                          content: const Text(
                                              'Your joining request has been sent to group admin'),
                                          textConfirm: 'Ok',
                                          confirmTextColor: white,
                                          onConfirm: () {
                                            Get.back();
                                          });
                                }
                              },
                              child: Container(
                                width: size * .9,
                                decoration: BoxDecoration(
                                  color: primary,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 15),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        groupData.isGroupJoined == 1
                                            ? 'Joined'
                                            : 'Join Group',
                                        style: const TextStyle(color: white),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      sizedBox(10, 0),
                      Container(
                        width: size * .9,
                        decoration: BoxDecoration(
                          color: white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10.0, horizontal: 10),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Info',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: black.withValues(alpha: .5),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Divider(
                                color: black.withValues(alpha: .1),
                                endIndent: 200,
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.group,
                                    color: black.withValues(alpha: .5),
                                    size: 18,
                                  ),
                                  sizedBox(0, 5),
                                  Text(
                                    '${groupData.membersCount} members',
                                    style:
                                        TextStyle(color: black.withValues(alpha: .5)),
                                  )
                                ],
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.visibility,
                                    color: black.withValues(alpha: .5),
                                    size: 18,
                                  ),
                                  sizedBox(0, 5),
                                  Text(
                                    groupData.privacy == '1'
                                        ? 'Public'
                                        : 'Private',
                                    style:
                                        TextStyle(color: black.withValues(alpha: .5)),
                                  )
                                ],
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.category,
                                    color: black.withValues(alpha: .5),
                                    size: 18,
                                  ),
                                  sizedBox(0, 5),
                                  Text(
                                    groupData.category!,
                                    style:
                                        TextStyle(color: black.withValues(alpha: .5)),
                                  )
                                ],
                              ),
                              Row(
                                children: [
                                  Icon(
                                    Icons.rss_feed,
                                    color: black.withValues(alpha: .5),
                                    size: 18,
                                  ),
                                  sizedBox(0, 5),
                                  Text(
                                    '${groupData.postCount} posts',
                                    style:
                                        TextStyle(color: black.withValues(alpha: .5)),
                                  )
                                ],
                              ),
                              sizedBox(10, 0),
                              Text(
                                'About',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: black.withValues(alpha: .5),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Divider(
                                color: black.withValues(alpha: .1),
                                endIndent: 200,
                              ),
                              Html(data: groupData.about)
                            ],
                          ),
                        ),
                      ),
                      Divider(
                        color: black.withValues(alpha: .1),
                      ),
                      groupData.isGroupJoined == 1
                          ? Center(
                              child: InkWell(
                                onTap: () async {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (_) => NewGroupPost(
                                                groupID: widget.groupID,
                                                name: groupData.name!,
                                                avatar: groupData.avatar!,
                                              )));
                                },
                                child: Container(
                                  width: size * .9,
                                  height: 180,
                                  decoration: BoxDecoration(
                                    color: white,
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(15.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Divider(),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              height: 35,
                                              width: 35,
                                              decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  image: DecorationImage(
                                                      image: groupData.avatar ==
                                                              '$siteUrl/upload/photos/d-group.jpg '
                                                          ? NetworkImage(
                                                              '$siteUrl/upload/photos/d-group.jpg')
                                                          : NetworkImage(
                                                              groupData
                                                                  .avatar!),
                                                      fit: BoxFit.cover)),
                                            ),
                                            Center(
                                              child: Text(
                                                inMind,
                                              ),
                                            ),
                                            Container(
                                              height: 20,
                                              width: 20,
                                              decoration: const BoxDecoration(
                                                shape: BoxShape.circle,
                                              ),
                                              child: SvgPicture.asset(
                                                'assets/svg/smile.svg',
                                              ),
                                            ),
                                          ],
                                        ),
                                        const Divider(),
                                        Row(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Expanded(
                                                child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.grey
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Center(
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceEvenly,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/svg/camera_icon.svg',
                                                        width: 17,
                                                      ),
                                                      Text(
                                                        'Photos',
                                                        style: TextStyle(
                                                            color: black
                                                                .withValues(
                                                                    alpha: 0.5),
                                                            fontSize: 13),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            )),
                                            sizedBox(0, 5),
                                            Expanded(
                                                child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.grey
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Center(
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceEvenly,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/svg/video_icon.svg',
                                                        width: 17,
                                                      ),
                                                      Text(
                                                        'Videos',
                                                        style: TextStyle(
                                                            color: black
                                                                .withValues(
                                                                    alpha: 0.5),
                                                            fontSize: 13),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            )),
                                            sizedBox(0, 5),
                                            Expanded(
                                                child: Container(
                                              decoration: BoxDecoration(
                                                color: Colors.grey
                                                    .withValues(alpha: 0.1),
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(8.0),
                                                child: Center(
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceEvenly,
                                                    children: [
                                                      SvgPicture.asset(
                                                        'assets/svg/smile.svg',
                                                        width: 17,
                                                      ),
                                                      Text(
                                                        'Feelings',
                                                        style: TextStyle(
                                                            color: black
                                                                .withValues(
                                                                    alpha: 0.5),
                                                            fontSize: 13),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            )),
                                          ],
                                        ),
                                        sizedBox(10, 0),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : Container(),
                      sizedBox(10, 0),
                      Text(
                        'Posts',
                        style: TextStyle(
                            color: black.withValues(alpha: .5),
                            fontWeight: FontWeight.bold,
                            fontSize: 18),
                      ),
                      sizedBox(10, 0),
                    ],
                  ),
                ),
                groupData.privacy == '1' || groupData.isOwner == true
                    ? GroupPostSection(groupID: groupData.groupId!)
                    : const Center(
                        child:
                            Text('You haven\'t permission to see private post'))
              ],
            ),
          );
        }
      }),
    );
  }
}
