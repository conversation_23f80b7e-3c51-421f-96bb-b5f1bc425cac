
import 'dart:convert';

GroupsDetailsModel groupsDetailsModelFromJson(String str) => GroupsDetailsModel.fromJson(json.decode(str));

String groupsDetailsModelToJson(GroupsDetailsModel data) => json.encode(data.toJson());

class GroupsDetailsModel {
  int? apiStatus;
  GroupData? groupData;

  GroupsDetailsModel({
    this.apiStatus,
    this.groupData,
  });

  factory GroupsDetailsModel.fromJson(Map<String, dynamic> json) => GroupsDetailsModel(
    apiStatus: json["api_status"],
    groupData: json["group_data"] == null ? null : GroupData.fromJson(json["group_data"]),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "group_data": groupData?.toJson(),
  };
}

class GroupData {
  String? userId;
  String? groupName;
  String? groupTitle;
  String? avatar;
  String? cover;
  String? about;
  String? category;
  String? subCategory;
  String? privacy;
  String? joinPrivacy;
  String? active;
  String? registered;
  String? time;
  String? groupId;
  String? url;
  String? name;
  String? categoryId;
  String? username;
  bool? isReported;
  String? groupSubCategory;
  List<dynamic>? fields;
  int? isGroupJoined;
  String? membersCount;
  String? postCount;
  bool? isOwner;

  GroupData({
    this.userId,
    this.groupName,
    this.groupTitle,
    this.avatar,
    this.cover,
    this.about,
    this.category,
    this.subCategory,
    this.privacy,
    this.joinPrivacy,
    this.active,
    this.registered,
    this.time,
    this.groupId,
    this.url,
    this.name,
    this.categoryId,
    this.username,
    this.isReported,
    this.groupSubCategory,
    this.fields,
    this.isGroupJoined,
    this.membersCount,
    this.postCount,
    this.isOwner,
  });


  void setIsJoined(int value) {
    isGroupJoined = value;
  }


  factory GroupData.fromJson(Map<String, dynamic> json) => GroupData(
    userId: json["user_id"],
    groupName: json["group_name"],
    groupTitle: json["group_title"],
    avatar: json["avatar"],
    cover: json["cover"],
    about: json["about"],
    category: json["category"],
    subCategory: json["sub_category"],
    privacy: json["privacy"],
    joinPrivacy: json["join_privacy"],
    active: json["active"],
    registered: json["registered"],
    time: json["time"],
    groupId: json["group_id"],
    url: json["url"],
    name: json["name"],
    categoryId: json["category_id"],
    username: json["username"],
    isReported: json["is_reported"],
    groupSubCategory: json["group_sub_category"],
    fields: json["fields"] == null ? [] : List<dynamic>.from(json["fields"]!.map((x) => x)),
    isGroupJoined: json["is_group_joined"],
    membersCount: json["members_count"],
    postCount: json["post_count"],
    isOwner: json["is_owner"],
  );

  Map<String, dynamic> toJson() => {
    "user_id": userId,
    "group_name": groupName,
    "group_title": groupTitle,
    "avatar": avatar,
    "cover": cover,
    "about": about,
    "category": category,
    "sub_category": subCategory,
    "privacy": privacy,
    "join_privacy": joinPrivacy,
    "active": active,
    "registered": registered,
    "time": time,
    "group_id": groupId,
    "url": url,
    "name": name,
    "category_id": categoryId,
    "username": username,
    "is_reported": isReported,
    "group_sub_category": groupSubCategory,
    "fields": fields == null ? [] : List<dynamic>.from(fields!.map((x) => x)),
    "is_group_joined": isGroupJoined,
    "members_count": membersCount,
    "post_count": postCount,
    "is_owner": isOwner,
  };
}
