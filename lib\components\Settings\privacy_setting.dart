import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Settings/privacy_items.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import '../../utils/colors.dart';
import '../size_box.dart';

class PrivacySetting extends StatefulWidget {
  const PrivacySetting({Key? key}) : super(key: key);

  @override
  State<PrivacySetting> createState() => _PrivacySettingState();
}

class _PrivacySettingState extends State<PrivacySetting> {
  String canFollow = 'Everyone';
  String canMessage = 'Everyone';
  String seeFriend = 'Everyone';
  String canPost = 'Everyone';
  String seeDob = 'Everyone';
  String confirmFollow = 'No';
  String lastSeen = 'Yes';
  String showActivities = 'Yes';
  String shareLocation = 'Yes';
  String searchEngine = 'Yes';
  String status = 'Online';

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Privacy'),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              privacyItems(size, 'Who can follow me?', canFollow, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              canFollow = 'Everyone';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Everyone'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              canFollow = 'People I Follow';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Who can message me?', canMessage, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              canMessage = 'Everyone';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Everyone'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              canMessage = 'People I Follow';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              canMessage = 'Nobody';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Nobody'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Who can see my friends?', seeFriend, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeFriend = 'Everyone';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Everyone'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeFriend = 'People I Follow';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeFriend = 'People Follow Me';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeFriend = 'Nobody';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Nobody'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Who can post on my timeline?', canPost, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              canPost = 'Everyone';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Everyone'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              canPost = 'People I Follow';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              canPost = 'Nobody';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Nobody'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Who can see my birthday?', seeDob, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeDob = 'Everyone';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Everyone'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeDob = 'People I Follow';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('People I Follow'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              seeDob = 'Nobody';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Nobody'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Confirm request when someone follows you?',
                  confirmFollow, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              confirmFollow = 'No';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              confirmFollow = 'Yes';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Show my last seen?', lastSeen, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              lastSeen = 'No';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              lastSeen = 'Yes';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Show my activities?', showActivities, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              showActivities = 'No';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              showActivities = 'Yes';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(size, 'Status', status, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              status = 'Online';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Online'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              status = 'Offline';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Offline'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(
                  size, 'Share my location with public?', shareLocation, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              shareLocation = 'No';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              shareLocation = 'Yes';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
              privacyItems(
                  size,
                  'Allow search engines to index my profile and posts?',
                  searchEngine, () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              searchEngine = 'No';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              searchEngine = 'Yes';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              }),
              sizedBox(10, 0),
            ],
          ),
        ),
      ),
    );
  }
}
