// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../../models/Pages/pages_details_model.dart';
import '../../utils/colors.dart';
import '../size_box.dart';

Widget topHeaderSection (PageData pageData, double size, context) {
  return Stack(
    alignment: Alignment.center,
    clipBehavior: Clip.none,
    children: [
      InkWell(
        onTap: (){
          showDialog(
              context: context,
              builder: (_) => AlertDialog(
                insetPadding: EdgeInsets.zero,
                contentPadding: EdgeInsets.zero,
                clipBehavior:
                Clip.antiAliasWithSaveLayer,
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(
                        Radius.circular(10.0))),
                content: Builder(builder: (context) {
                  return Image.network(
                    pageData.cover!,
                    fit: BoxFit.cover,
                  );
                }),
              ));
        },
        child: Container(
          height: 200,
          decoration: BoxDecoration(
            color: white,
            image: DecorationImage(
              image: NetworkImage(pageData.cover!),
              fit: BoxFit.cover,
            ),
          ),
        ),
      ),
      Positioned(
        bottom: -40,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10.0),
          child: Row(
            children: [
              InkWell(
                onTap: (){
                  showDialog(
                      context: context,
                      builder: (_) => AlertDialog(
                        insetPadding: EdgeInsets.zero,
                        contentPadding: EdgeInsets.zero,
                        clipBehavior:
                        Clip.antiAliasWithSaveLayer,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(
                                Radius.circular(10.0))),
                        content: Builder(builder: (context) {
                          return Image.network(
                            pageData.avatar!,
                            fit: BoxFit.cover,
                          );
                        }),
                      ));
                },
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                      color: white,
                      borderRadius: BorderRadius.circular(15),
                      border: Border.all(
                        color: background,
                        width: 1
                      ),
                      image: DecorationImage(
                        image: NetworkImage(pageData.avatar!),
                        fit: BoxFit.cover,
                      )),
                ),
              ),
              sizedBox(0, 10),
              Container(
                width: size * .650,
                decoration: BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10, vertical: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Text(
                            pageData.name!,
                            style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600),
                          ),
                          sizedBox(0, 5),
                          pageData.verified == '1'
                              ? SvgPicture.asset(
                            'assets/svg/verify.svg',
                            width: 15,
                          )
                              : Container(),
                        ],
                      ),
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(.1),
                            borderRadius:
                            BorderRadius.circular(5)),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 2),
                          child: Text(pageData.category!,
                              style: TextStyle(
                                  color: black.withOpacity(.5),
                                  fontSize: 10)),
                        ),
                      ),
                      sizedBox(2, 0),
                      pageData.website!.isEmpty
                          ? Container()
                          : Row(
                        children: [
                          SvgPicture.asset(
                            'assets/svg/global.svg',
                            color: black.withOpacity(.5),
                            width: 12,
                          ),
                          sizedBox(0, 3),
                          Text(
                            pageData.website!,
                            style: TextStyle(
                                color:
                                black.withOpacity(.5),
                                fontSize: 10),
                          )
                        ],
                      ),
                      Row(
                        children: [
                          Text(pageData.likesCount!),
                          sizedBox(0, 2),
                          Text(
                            'Followers',
                            style: TextStyle(
                                color: black.withOpacity(.5)),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      )
    ],
  );
}