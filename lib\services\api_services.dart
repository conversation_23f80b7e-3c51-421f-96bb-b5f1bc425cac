// ignore_for_file: depend_on_referenced_packages, empty_catches, prefer_if_null_operators, use_build_context_synchronously

import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/Groups/group_details_controller.dart';
import 'package:flutter_wowonder/controllers/comments/comment_reply_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_page_post_controller.dart';
import 'package:flutter_wowonder/models/Comments/comment_reply_model.dart';
import 'package:flutter_wowonder/models/Groups/group_details_model.dart';
import 'package:flutter_wowonder/models/PostModels/get_group_post_model.dart';

import 'package:get/get.dart';
import 'package:flutter_wowonder/models/PostModels/get_all_post_model.dart';
import 'package:http/http.dart' as http;
import 'package:media_picker_widget/media_picker_widget.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path/path.dart' as path;
import '../components/navigation.dart';
import '../components/response_error_toast.dart';
import '../controllers/Story/get_all_stories.dart';
import '../controllers/UserData/get_user_data_controller.dart';
import '../controllers/auth/pw_token_controller.dart';
import '../controllers/comments/get_all_comments_controller.dart';
import '../controllers/posts/get_all_post_controller.dart';
import '../models/Comments/post_comment_model.dart';
import '../models/Groups/get_groups_model.dart';
import '../models/Groups/member_request_model.dart';
import '../models/Notification/notification_model.dart';
import '../models/Pages/get_pages_model.dart';
import '../models/Pages/pages_details_model.dart';
import '../models/PostModels/get_page_post_model.dart';
import '../models/PostModels/get_saved_post_model.dart';
import '../models/PostModels/get_user_post_model.dart';
import '../models/Search/recent_search_model.dart';
import '../models/Search/search_results_model.dart';
import '../models/UserData/get_user_details_model.dart';
import '../models/UserData/get_user_followers_model.dart';

class APISERvices {
  static TKController controller = Get.put(TKController());

  // License Validation
  // Future<bool?> appValidation(purchaseCode) async {
  //   const String token = 'jey73skieyrwy732455sadawss3';
  //   final String siteUrl =
  //       "https://api.envato.com/v3/market/author/sale?code=$purchaseCode";
  //   http.Response response = await http.get(Uri.parse(siteUrl), headers: {
  //     'Authorization': 'Bearer $token',
  //     'User-Agent': 'Purchase code verification'
  //   });
  //   try {
  //     if (response.statusCode == 200) {
  //       final result = await SharedService.isLoggedIn();
  //       if (result == true) {
  //         navigate('home');
  //       } else {
  //         navigate('login');
  //       }
  //       return true;
  //     } else {
  //       navigate('error');
  //       return false;
  //     }
  //   } catch (e) {
  //     navigate('error');
  //     return false;
  //   }
  // }
//  ... other code
  static const Map<String, String> accounts = {
    //  ...
    'auth': '/auth', //  الجديد جدأ  السطر
    //  ...
  };
  // Login
  Future<void> userLogin(String email, String password) async {
    final pref = await SharedPreferences.getInstance();
    final response =
        await http.post(Uri.parse(controller.total['sd'][0]['ln']), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'username': email,
      'password': password
    });

    try {
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        if (jsonData['api_status'] != 200) {
          responseError(jsonData);
        } else {
          pref.setString('token', jsonData['access_token']);
          pref.setString('id', jsonData['user_id']);
          GetAllPostController().getData(tokenID: jsonData['access_token']);
          GetAllStoriesController().getData(tokenID: jsonData['access_token']);
          navigate('home');
        }
      } else {}
    } catch (e) {}
  }

  // Get News Feed //
  Future<DataModel?> getAllPost(
      {String? tokenID, String? afterPostID, String? postType}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}${token ?? tokenID}'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "get_news_feed",
          'post_type': postType ?? '',
          "limit": "20",
          "after_post_id": afterPostID
        },
      );
      if (response.statusCode == 200) {
        return dataModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {
      throw Future.error(e);
    }
  }

  // Member Request
  Future<MemberRequestModel?> memberRequest({required String groupID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['mr']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "get_requests",
          'group_id': groupID,
        },
      );
      if (response.statusCode == 200) {
        return memberRequestModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  static Future acceptRequest(
      {required String groupID,
      required String userID,
      required String type}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['ar']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": type,
          'group_id': groupID,
          'user_id': userID
        },
      );
      if (response.statusCode == 200) {
        Get.snackbar(
          'Accepted',
          'Member required action completed!',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 1),
        );
        return memberRequestModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // Get User Post //
  Future<UserPostModel?> getUserPost({String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "get_user_posts",
          'id': userID,
        },
      );
      if (response.statusCode == 200) {
        return userPostModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {}
    return null;
  }

  Future<SavedPostModel?> getSavedPost() async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "saved",
        },
      );
      if (response.statusCode == 200) {
        return savedPostModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {
      return null;
    }
  }

  // Get Group Post
  Future<GroupPostModel?> getGroupPost({String? groupID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "get_group_posts",
          'id': groupID,
        },
      );
      if (response.statusCode == 200) {
        return groupPostModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {}
    return null;
  }

  // Get Pages Post
  Future<PagePostModel?> getPagePost(
      {String? tokenID, String? afterPostID, required String pageID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": "get_page_posts",
          // "limit": "1",
          'id': pageID,
          // "after_post_id": afterPostID
        },
      );
      if (response.statusCode == 200) {
        return pagePostModelFromJson(response.body);
      } else {
        return null;
      }
    } catch (e) {}
    return null;
  }

  // Search Result
  static Future<SearchResultsModel?> searchResult(
      {required String searchText}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['sr']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'search_key': searchText
    });
    try {
      if (response.statusCode == 200) {
        return searchResultsModelFromJson(response.body);
      } else {}
    } catch (e) {}
    return searchResultsModelFromJson(response.body);
  }

  // Recent Search
  static Future<RecentSearchModel?> recentSearch() async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['rs']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
    });
    try {
      if (response.statusCode == 200) {
        return recentSearchModelFromJson(response.body);
      } else {}
    } catch (e) {}
    return recentSearchModelFromJson(response.body);
  }

  // New Post //
  Future addNewPost({
    List<Media>? mediaList,
    String? postText,
    String? privacy,
    String? pageID,
    String? userID,
    String? groupID,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    Map<String, String> body = {
      "user_id": userID == null ? '' : userID,
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      "postText": postText!,
      "postPrivacy": privacy!,
    };
    var request = http.MultipartRequest(
        'POST', Uri.parse('${controller.total['sd'][0]['anp']}$token'));

    if (mediaList != null) {
      for (int a = 0; a < mediaList.length; a++) {
        var fileType = path.extension(mediaList[a].file!.path);
        if (fileType == '.jpg' ||
            fileType == '.jpeg' ||
            fileType == '.png' ||
            fileType == '.gif') {
          request.files.add(await http.MultipartFile.fromPath(
              'postPhotos[]', mediaList[a].file!.path));
        } else if (fileType == '.mp4' ||
            fileType == '.m4v' ||
            fileType == '.webm' ||
            fileType == '.flv' ||
            fileType == '.mov' ||
            fileType == '.mpeg' ||
            fileType == '.mkv') {
          request.files.add(await http.MultipartFile.fromPath(
              'postVideo', mediaList[a].file!.path));
        } else {}
      }
    } else {}

    request.fields.addAll(body);
    http.StreamedResponse response = await request.send();
    if (response.statusCode == 200) {
      if (pageID != null) {
        GetPagePostController().getData(pageID: pageID);
      }
    } else {}
  }

  // Post Comments //
  Future<PostCommentModel?> getAllComment(
      {String? postID,
      String? commentID,
      required String type,
      String? commentText}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['cm']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'type': type,
      'post_id': postID
    });
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] != 200) {
      } else {
        return postCommentModelFromJson(response.body);
      }
    } else {}
    return null;
  }

  Stream<PostCommentModel?> streamComment(
      {Duration? refreshTime,
      required String postID,
      required String type}) async* {
    while (true) {
      await Future.delayed(const Duration(seconds: 1));
      yield await getAllComment(postID: postID, type: type);
    }
  }

  // Account Delete
  static Future deleteAccount(
      {required String password, required BuildContext context}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['da']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'password': password,
    });
    if (response.statusCode == 200) {
      final jsonData = jsonDecode(response.body);
      if (jsonData['api_status'] == 200) {
        Get.snackbar('Deleted', 'Your account has been deleted successfully!');
        pref.remove('token');
        Get.offNamedUntil('login', (route) => false);
      } else {
        Get.snackbar('Error!', jsonData['errors']['error_text']);
      }
    } else {}
  }

  // Notification
  Future<NotificationsModel?> getAllNotification() async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http
          .post(Uri.parse('${controller.total['sd'][0]['an']}$token'), body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'fetch': 'notifications',
      });
      if (response.statusCode == 200) {
        return notificationsModelFromJson(response.body);
      } else {}
    } catch (e) {}
    return null;
  }

  Stream<NotificationsModel?> streamNotification() async* {
    while (true) {
      await Future.delayed(const Duration(seconds: 1));
      yield await getAllNotification();
    }
  }

  // Comment Reply
  Future<CommentReplyModel?> commentReply(
      {String? commentID, required String type, String? commentText}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['cm']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'type': type,
      'comment_id': commentID,
    });
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] != 200) {
      } else {
        return commentReplyModelFromJson(response.body);
      }
    } else {}
    return null;
  }

  // Create Comment
  static Future commentAction({
    String? commentText,
    String? postID,
    required String type,
    String? commentID,
    String? replyID,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final commentController = Get.put(GetAllPostCommentController());
    final commentReplyController = Get.put(CommentReplyController());
    http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['cm']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'type': type,
      'text': commentText == null ? '' : commentText,
      'post_id': postID == null ? '' : postID,
      'comment_id': commentID == null ? '' : commentID,
      'reply_id': replyID == null ? '' : replyID,
    });
    try {
      if (response.statusCode == 200) {
        type == 'fetch_comments'
            ? commentController.getData(
                type: 'fetch_comments',
                postID: postID!,
              )
            : commentReplyController.getData(
                type: 'fetch_comments_reply',
                commentID: commentID!,
              );
      } else {}
    } catch (e) {}
  }

  // User Verification
  static Future userVerification({
    required String docPath,
    required String selfiePath,
    required String name,
    required String message,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    var request = http.MultipartRequest(
        'POST', Uri.parse('${controller.total['sd'][0]['uv']}$token'));
    request.fields.addAll({
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'name': name,
      'text': message
    });
    request.files.add(await http.MultipartFile.fromPath('passport', docPath));
    request.files.add(await http.MultipartFile.fromPath('photo', selfiePath));

    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
      navigate('verifyReqSuccess');
    } else {
      Get.snackbar('Something Wrong',
          'Hey! Something is wrong, please try again few moments later');
    }
  }

  // Page Verification
  static Future pageVerification({required String pageID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['pv']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'id': pageID,
      },
    );
    if (response.statusCode == 200) {
      Get.snackbar(
        'Success',
        'Your verification request has been sent successfully!',
      );
    } else {
      return null;
    }
  }

  // Get User Stories //
  Future<Map?> getAllStories({String? tokenID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['as']}${token ?? tokenID}'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return null;
      }
    } catch (e) {}
    return null;
  }

  // Create Story
  static Future createStory({
    required String filePath,
    required String filesType,
    required String storyTitle,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    Map<String, String> body = {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      "file_type": filesType,
      "story_title": storyTitle
    };
    var request = http.MultipartRequest(
        'POST', Uri.parse('${controller.total['sd'][0]['cs']}$token'));

    var fileType = path.extension(filePath);
    if (fileType == '.jpg' ||
        fileType == '.jpeg' ||
        fileType == '.png' ||
        fileType == '.gif') {
      request.files.add(await http.MultipartFile.fromPath('file', filePath));
    } else if (fileType == '.mp4' ||
        fileType == '.m4v' ||
        fileType == '.webm' ||
        fileType == '.flv' ||
        fileType == '.mov' ||
        fileType == '.mpeg' ||
        fileType == '.mkv') {
      request.files.add(await http.MultipartFile.fromPath('file', filePath));
    }

    request.fields.addAll(body);
    http.StreamedResponse response = await request.send();
    if (response.statusCode == 200) {
    } else {}
  }

  static Future storyReaction({
    required String storyID,
    required String reactType,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['strr']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          'id': storyID,
          'reaction': reactType,
        },
      );
      if (response.statusCode == 200) {
      } else {
        return null;
      }
    } catch (e) {}
  }

  // Share Post
  static Future postShare(
      {required String postID, required String shareText}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final id = pref.getString('id');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pst']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          'user_id': id,
          'type': 'share_post_on_timeline',
          'id': postID,
          'text': shareText
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        return null;
      }
    } catch (e) {}
  }

  // Report
  static Future reportPage(
      {required String pageID, required String text}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['rp']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "page_id": pageID,
          'text': text,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
  }

  static Future reportGroup(
      {required String groupID, required String text}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['rg']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "group_id": groupID,
          'text': text,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {
      return null;
    }
  }

  static Future reportUser(
      {required String userID, required String text}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['ru']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "user": userID,
          'text': text,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
  }

  // Like Page
  static Future pageLike({required String pageID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['lp']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "page_id": pageID,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
  }

  // Join Group
  static Future joinGroup({required String groupID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');

    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['jg']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "group_id": groupID,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
  }

  // Follow User
  static Future followUser({required String userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['fu']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "user_id": userID,
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
  }

  // Post Reaction //
  Future<Map?> postReaction(
      {required String postID, String? action, String? count}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pa']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "post_id": postID,
          'action': action,
          "reaction": count
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
    return null;
  }

  // Comment Reaction
  Future<Map?> commentReaction(
      {required String commentID,
      required String action,
      String? reaction}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['cm']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "comment_id": commentID,
          'type': action,
          'reaction': reaction
        },
      );
      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
    } catch (e) {}
    return null;
  }

  // Comment Report
  Future<Map?> commentReport({required String commentID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    http.Response response = await http
        .post(Uri.parse('${controller.total['sd'][0]['rc']}$token'), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'comment_id': commentID
    });
    try {
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded['api_status'] == 200) {
          return decoded;
        }
      }
    } catch (e) {}
    return null;
  }

  // Post Action //
  Future<Map?> postAction(
      {required String postID, required String action}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pa']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "post_id": postID,
          'action': action,
        },
      );
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (action == 'report') {
          if (decoded['api_status'] == 200) {
            Get.snackbar('Reported', 'Post reported to admin successfully!');
          } else {
            Get.snackbar('Error', 'an error occurred!');
          }
        } else if (action == 'save') {
          if (decoded['api_status'] == 200) {
            Get.snackbar('Saved', 'Post saved successfully!');
          } else {
            Get.snackbar('Error', 'an error occurred!');
          }
        } else if (action == 'delete') {
          if (decoded['api_status'] == 200) {
            Get.snackbar('Deleted', 'Post deleted successfully!');
          } else {
            Get.snackbar('Error', 'an error occurred!');
          }
        } else if (action == 'disable_comments') {
          if (decoded['api_status'] == 200) {
            Get.snackbar('Disabled',
                'Your post comment has been disabled successfully!');
          } else {
            Get.snackbar('Error', 'an error occurred!');
          }
        }
        return jsonDecode(response.body);
      }
    } catch (e) {}
    return null;
  }

  Future<Map?> hidePost({required String postID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['hp']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "post_id": postID,
        },
      );
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded['api_status'] == 200) {
          Get.snackbar('Success', 'The post has been hide from your profile!');
        } else {
          Get.snackbar(
              'Error', 'Sorry, you can\'t able to hide your own post!');
        }
        return jsonDecode(response.body);
      }
    } catch (e) {}
    return null;
  }

  // Update User Details
  static Future updateUserData({
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? gender,
    String? birthday,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final profile = Get.put(UserDataController());
    final data = profile.getUserDetails.value;
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['uud']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          'first_name': firstName == null ? data.firstName : firstName,
          'last_name': lastName == null ? data.lastName : lastName,
          'phone_number': phoneNumber == null ? data.phnNumber : phoneNumber,
          'email': email == null ? data.email : email,
          'birthday': birthday == null ? data.birthday : birthday,
          'gender': gender == null ? data.gender : gender,
        },
      );
      if (response.statusCode == 200) {
        final decoded = jsonDecode(response.body);
        if (decoded['api_status'] == 200) {
          Get.snackbar('Success', 'Profile update successfully!');
          UserDataController().refreshData();
        } else {
          Get.snackbar('Error', 'Sorry, you can\'t able to edit your profile!');
        }
      }
    } catch (e) {}
  }

  static Future updatePassword(
      {required String currentPassword,
      required String newPassword,
      required BuildContext context}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['uud']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'current_password': currentPassword,
        'new_password': newPassword,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Password Update Successfully!');
        Navigator.pop(context);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updateLifeEvent(
      {String? school,
      String? working,
      String? website,
      String? relationship,
      required BuildContext context}) async {
    final profile = Get.put(UserDataController());
    final data = profile.getUserDetails.value;
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['uud']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'working': working == null ? data.working : working,
        'school': school == null ? data.school : school,
        'working_link': website == null ? data.companyWebsite : website,
        'relationship':
            relationship == null ? data.relationshipID : relationship,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        Navigator.pop(context);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updateSocial(
      {String? facebook,
      String? twitter,
      String? vk,
      String? linkedin,
      String? instagram,
      String? youtube,
      required BuildContext context}) async {
    final profile = Get.put(UserDataController());
    final data = profile.getUserDetails.value;
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['uud']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'facebook': facebook ?? data.facebook,
        'twitter': twitter ?? data.twitter,
        'instagram': instagram ?? data.instagram,
        'youtube': youtube ?? data.youtube,
        'vk': vk ?? data.vk,
        'linkedin': linkedin ?? data.linkedin,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        Navigator.pop(context);
        UserDataController().refreshData;
        UserDataController().refreshData;
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updateAbout(
      {String? about, required BuildContext context}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['uud']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'about': about
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        Navigator.pop(context);
        UserDataController().refreshData;
        UserDataController().refreshData;
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updateAvatar(
      {required String filePath, required BuildContext context}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    var headers = {
      'Cookie':
          '_us=1685375278; ad-con=%7B%26quot%3Bdate%26quot%3B%3A%26quot%3B2023-05-28%26quot%3B%2C%26quot%3Bads%26quot%3B%3A%5B%5D%7D; PHPSESSID=3ee26428af7feb2dbc6888b5360f1926; mode=day'
    };
    var request = http.MultipartRequest(
        'POST', Uri.parse('${controller.total['sd'][0]['uud']}$token'));
    request.fields.addAll({'server_key': '6e2ca3e6f7e3aa6b4ffcd04bee494673'});
    request.files.add(await http.MultipartFile.fromPath('avatar', filePath));
    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
    } else {}
  }

  static Future updateCover(
      {required String filePath, required BuildContext context}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    var headers = {
      'Cookie':
          '_us=1685215056; ad-con=%7B%26quot%3Bdate%26quot%3B%3A%26quot%3B2023-05-26%26quot%3B%2C%26quot%3Bads%26quot%3B%3A%5B%5D%7D; _us=1685216935; ad-con=%7B%26quot%3Bdate%26quot%3B%3A%26quot%3B2023-05-26%26quot%3B%2C%26quot%3Bads%26quot%3B%3A%5B%5D%7D; mode=day; src=1'
    };

    var request = http.MultipartRequest(
        'POST', Uri.parse('${controller.total['sd'][0]['uud']}$token'));
    request.fields.addAll(
        {controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk']});
    request.files.add(await http.MultipartFile.fromPath('cover', filePath));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();

    if (response.statusCode == 200) {
    } else {}
  }

  // Update Page Data
  static Future updatePageGeneral({
    required String pageID,
    String? pageName,
    String? userName,
    required BuildContext context,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['upd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'page_id': pageID,
        'page_title': pageName,
        'page_name': userName,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updatePageInfo({
    required String pageID,
    String? company,
    String? phone,
    String? location,
    String? website,
    required BuildContext context,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['upd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'page_id': pageID,
        'website': website,
        'phone': phone,
        'address': location,
        'company': company
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updatePageSocial(
      {String? facebook,
      String? twitter,
      String? vk,
      String? linkedin,
      String? instagram,
      String? youtube,
      required BuildContext context}) async {
    final profile = Get.put(UserDataController());
    final data = profile.getUserDetails.value;
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['upd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'facebook': facebook ?? data.facebook,
        'twitter': twitter ?? data.twitter,
        'instagram': instagram ?? data.instagram,
        'youtube': youtube ?? data.youtube,
        'vk': vk ?? data.vk,
        'linkedin': linkedin ?? data.linkedin,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        Navigator.pop(context);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updatePageAbout({
    String? about,
    required BuildContext context,
    required String pageID,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['upd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'page_description': about,
        'page_id': pageID
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        Navigator.pop(context);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  // Update Group Data
  static Future updateGroupGeneral({
    required String groupID,
    String? groupName,
    String? userName,
    String? about,
    required BuildContext context,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['ugd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'group_id': groupID,
        'group_title': groupName,
        'group_name': userName,
        'about': about
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        GroupDetailsController().getData(pageID: groupID);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  static Future updateGroupPrivacy({
    required String groupID,
    String? privacy,
    String? joiningPrivacy,
    required BuildContext context,
  }) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['ugd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        'group_id': groupID,
        'privacy': privacy,
        'join_privacy': joiningPrivacy,
      },
    );
    if (response.statusCode == 200) {
      final decoded = jsonDecode(response.body);
      if (decoded['api_status'] == 200) {
        Get.snackbar('Success', 'Data update successfully!');
        GroupDetailsController().getData(pageID: groupID);
      } else {
        Get.snackbar('Error', decoded['errors']['error_text']);
      }
    }
  }

  // Get User Details //
  static Future<UserDetailsModel?> getUserData({String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final id = pref.getString('id');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['ud']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "fetch": "user_data",
          'user_id': userID ?? id,
        },
      );
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        pref.setString('name', jsonData['user_data']['name']);
        return userDetailsModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get Pages
  static Future<GetPagesModel?> getPages(
      {required String type, String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['mp']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": type,
          'user_id': userID ?? '',
        },
      );
      if (response.statusCode == 200) {
        return getPagesModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get Groups
  static Future<GetGroupsModel?> getGroups(
      {required String type, String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final id = pref.getString('id');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['mg']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": type,
          'user_id': id
        },
      );
      if (response.statusCode == 200) {
        return getGroupsModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get Recommended
  static Future<GetPagesModel?> getRecommended({required String type}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['fr']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": type,
        },
      );
      if (response.statusCode == 200) {
        return getPagesModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  static Future<GetGroupsModel?> groupsRecommerded(
      {required String type}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['fr']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "type": type,
        },
      );
      if (response.statusCode == 200) {
        return getGroupsModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get Page Data
  static Future<PageDetailsModel?> getPageData({required String pageID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['pd']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "page_id": pageID,
        },
      );
      if (response.statusCode == 200) {
        return pageDetailsModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get Group Data
  static Future<GroupsDetailsModel?> getGroupData(
      {required String groupID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    try {
      final http.Response response = await http.post(
        Uri.parse('${controller.total['sd'][0]['gd']}$token'),
        body: {
          controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
          "group_id": groupID,
        },
      );
      if (response.statusCode == 200) {
        return groupsDetailsModelFromJson(response.body);
      }
    } catch (e) {
      return null;
    }
    return null;
  }

  // Get User Followers
  Future<UserFollowersModel?> getUserFollowers({String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final token = pref.getString('token');
    final id = pref.getString('id');
    final http.Response response = await http.post(
      Uri.parse('${controller.total['sd'][0]['urd']}$token'),
      body: {
        controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
        "fetch": "followers",
        'user_id': userID ?? id,
      },
    );
    if (response.statusCode == 200) {
      return userFollowersModelFromJson(response.body);
    }
    return null;
  }
}
