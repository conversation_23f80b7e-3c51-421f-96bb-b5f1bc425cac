name: flutter_wowonder
description: A social media application.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+0

environment:
  sdk: '>=2.19.3 <3.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  get: ^4.6.6
  http: ^1.2.1
  media_picker_widget: ^1.0.7
  smooth_page_indicator: ^1.0.0
  flutter_speed_dial: ^7.0.0
  shared_preferences: ^2.2.3
  flutter_svg: ^2.0.10+1
  lottie: ^3.1.2
  zoom_tap_animation: ^1.1.0
  better_player:
    git:
      url: https://github.com/matthewrice345/betterplayer.git
      ref: Issue1057
  shimmer: ^3.0.0
  flutter_html: ^3.0.0-beta.2
  audioplayers: ^6.0.0
  encrypt: ^5.0.3
  chewie: ^1.5.0
  video_player: ^2.8.6
  image_picker: ^1.1.2
  image_cropper: ^7.0.4
  refresh_loadmore: ^2.0.5
  url_launcher: ^6.3.0
  firebase_core: ^2.13.0
  firebase_analytics: ^10.7.0 # تأكد من إضافة هذه المكتبة
  firebase_crashlytics: ^3.3.0 # تأكد من إضافة هذه المكتبة
  awesome_notifications: ^0.9.3+1
  awesome_notifications_fcm: ^0.9.3+1
  story: ^1.1.0
  story_creator: ^1.0.2
  like_button: ^2.0.5
  photo_manager: ^3.1.1
  onesignal_flutter: ^5.2.0
  wakelock: ^0.6.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/icon/
    - assets/images/reactions/
    - assets/json/
    - assets/svg/
    - assets/gif/
    - assets/audios/

  fonts:
    - family: Lato
      fonts:
        - asset: assets/fonts/Lato/Lato-Black.ttf
        - asset: assets/fonts/Lato/Lato-Bold.ttf
        - asset: assets/fonts/Lato/Lato-Light.ttf
        - asset: assets/fonts/Lato/Lato-Regular.ttf
        - asset: assets/fonts/Lato/Lato-Thin.ttf
    - family: Poppins
      fonts:
        - asset: assets/fonts/Poppins/Poppins-Black.ttf
        - asset: assets/fonts/Poppins/Poppins-Bold.ttf
        - asset: assets/fonts/Poppins/Poppins-ExtraBold.ttf
        - asset: assets/fonts/Poppins/Poppins-ExtraLight.ttf
        - asset: assets/fonts/Poppins/Poppins-Light.ttf
        - asset: assets/fonts/Poppins/Poppins-Thin.ttf
        - asset: assets/fonts/Poppins/Poppins-Medium.ttf
        - asset: assets/fonts/Poppins/Poppins-Regular.ttf
        - asset: assets/fonts/Poppins/Poppins-SemiBold.ttf
