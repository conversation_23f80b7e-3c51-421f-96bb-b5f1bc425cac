import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/auth/pw_token_controller.dart';
import 'package:flutter_wowonder/screens/Auth/ForgotPassword/confirmation_code_screen.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../../components/response_error_toast.dart';

class ForgetPasswordController extends GetxController {
  static TKController controller = Get.put(TKController());
  var isLoading = false.obs;
  final emailController = TextEditingController();

  Future<void> forgetPassword({required String email, required context}) async {
    isLoading.value = true;
    final response =
        await http.post(Uri.parse(controller.total['sd'][0]['rpe']), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      "email": email
    });
    if (response.statusCode == 200) {
      isLoading.value = false;
      final jsonData = jsonDecode(response.body);
      if (jsonData['api_status'] == 200) {
        isLoading.value = false;
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (_) => ResetCodeScreen(
                      email: emailController.text,
                    )));
      } else {
        isLoading.value = false;
        responseError(jsonData);
      }
    } else {
      isLoading.value = false;
    }
  }
}
