import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/SearchScreen/search_result_screen.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:get/get.dart';

class SearchScreen extends StatelessWidget {
  const SearchScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final textController = TextEditingController();
    return Scaffold(
      body: SafeArea(
          child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey.withOpacity(.1),
                    ),
                    child: IconButton(
                        onPressed: () {
                          Get.back();
                        },
                        icon: const Icon(
                          Icons.arrow_back_ios_new,
                          color: black,
                        )),
                  ),
                  sizedBox(0, 5),
                  Expanded(
                    flex: 6,
                    child: Container(
                      width: MediaQuery.of(context).size.width * .7,
                      height: 60,
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(10),
                              bottomLeft: Radius.circular(10)),
                          border: Border.all(color: primary)),
                      child: Center(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 15.0),
                          child: TextField(
                            controller: textController,
                            decoration: const InputDecoration(
                              hintText: 'Type Something...',
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => SearchResultScreen(
                              userInput: textController.text.toString(),
                            ),
                          ),
                        );
                      },
                      child: Container(
                        width: MediaQuery.of(context).size.width * .150,
                        height: 60,
                        decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color: black.withOpacity(.200), blurRadius: 1)
                            ],
                            color: primary,
                            borderRadius: const BorderRadius.only(
                                topRight: Radius.circular(10),
                                bottomRight: Radius.circular(10))),
                        child: const Center(
                          child: Icon(
                            Icons.search,
                            color: white,
                          ),
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 15.0,
                vertical: 10,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recent',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  sizedBox(10, 0),
                  FutureBuilder(
                      future: APISERvices.recentSearch(),
                      builder: (_, snapshot) {
                        if (snapshot.connectionState ==
                            ConnectionState.waiting) {
                          return searchResultShimmer(size);
                        } else {
                          return ListView.builder(
                              shrinkWrap: true,
                              primary: false,
                              itemCount: snapshot.data!.data!.length,
                              itemBuilder: (_, index) {
                                final data = snapshot.data!.data![index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: Row(
                                    children: [
                                      CircleAvatar(
                                        backgroundImage:
                                            NetworkImage(data.avatar!),
                                      ),
                                      sizedBox(0, 10),
                                      Text(
                                        data.name!,
                                        style: const TextStyle(fontSize: 18),
                                      )
                                    ],
                                  ),
                                );
                              });
                        }
                      })
                ],
              ),
            )
          ],
        ),
      )),
    );
  }
}
