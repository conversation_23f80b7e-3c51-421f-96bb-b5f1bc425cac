import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import '../../../controllers/Pages/pages_details_controller.dart';
import '../../../services/api_services.dart';
import '../../../utils/colors.dart';
import '../../../widgets/button_widget.dart';

class PageAboutSetting extends StatefulWidget {
  final String pageID;
  const PageAboutSetting({Key? key, required this.pageID}) : super(key: key);

  @override
  State<PageAboutSetting> createState() => _PageAboutSettingState();
}

class _PageAboutSettingState extends State<PageAboutSetting> {
  final about = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final pageData = Get.put(PageDetailsController());
    final data = pageData.pageData.value;
    about.text = data.about!;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'About'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
          vertical: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              decoration: BoxDecoration(
                  border: Border.all(
                    color: black.withOpacity(.1),
                  ),
                  borderRadius: BorderRadius.circular(15)),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: TextField(
                  controller: about,
                  keyboardType: TextInputType.multiline,
                  minLines: 2,
                  maxLines: 10,
                  decoration: const InputDecoration(
                      border: InputBorder.none,
                      label: Text('About You'),
                      hintText: 'Describe about you!'),
                ),
              ),
            ),
            sizedBox(20, 0),
            CustomButton(
              title: 'Save',
              onTap: () {
                APISERvices.updatePageAbout(
                  context: context,
                  pageID: widget.pageID,
                  about: about.text,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
