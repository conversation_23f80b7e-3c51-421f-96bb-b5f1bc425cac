import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';

import '../../components/size_box.dart';
import '../../controllers/Groups/member_request_controller.dart';

class MemberRequestScreen extends StatefulWidget {
  final String groupID;

  const MemberRequestScreen({Key? key, required this.groupID})
      : super(key: key);

  @override
  State<MemberRequestScreen> createState() => _MemberRequestScreenState();
}

class _MemberRequestScreenState extends State<MemberRequestScreen> {
  final controller = Get.put(MemberRequestController());

  @override
  void initState() {
    controller.getData(groupID: widget.groupID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Member Request'),
      body: Obx(() {
        if (controller.isLoading.value) {
          return searchResultShimmer(size);
        } else {
          return controller.requestData.value.data!.isEmpty
              ? const Center(
                  child: Text('No new request'),
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    children: [
                      ListView.builder(
                          shrinkWrap: true,
                          primary: false,
                          itemCount: controller.requestData.value.data!.length,
                          itemBuilder: (_, index) {
                            final data = controller
                                .requestData.value.data![index].userData;
                            return Container(
                              margin: const EdgeInsets.only(
                                bottom: 10,
                              ),
                              decoration: BoxDecoration(
                                color: white,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8.0),
                                child: ListTile(
                                  leading: CircleAvatar(
                                    radius: 25,
                                    backgroundImage:
                                        NetworkImage(data!.avatar!),
                                  ),
                                  title: Row(
                                    children: [
                                      Text(data.name!),
                                      data.isVerified == '1'
                                          ? SvgPicture.asset(
                                              'assets/svg/verify.svg',
                                              width: 15,
                                            )
                                          : Container(),
                                      data.isPro == '1'
                                          ? Image.asset(
                                              'assets/svg/vip.png',
                                              width: 15,
                                            )
                                          : Container(),
                                    ],
                                  ),
                                  subtitle: Row(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          controller.setAction(
                                            userID: data.userId!,
                                            index: index,
                                            groupID: widget.groupID,
                                            type: 'accept_request',
                                          );
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: primary,
                                            borderRadius:
                                                BorderRadius.circular(15),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 8.0,
                                              horizontal: 10,
                                            ),
                                            child: Center(
                                              child: Row(
                                                children: const [
                                                  Icon(
                                                    Icons.done,
                                                    color: white,
                                                  ),
                                                  Text(
                                                    'Confirm',
                                                    style: TextStyle(
                                                        color: white,
                                                        fontWeight:
                                                            FontWeight.w500),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      sizedBox(0, 5),
                                      InkWell(
                                        onTap: () {
                                          controller.setAction(
                                            userID: data.userId!,
                                            index: index,
                                            groupID: widget.groupID,
                                            type: 'delete_request',
                                          );
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                            color: Colors.red,
                                            borderRadius:
                                                BorderRadius.circular(15),
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                              vertical: 8.0,
                                              horizontal: 10,
                                            ),
                                            child: Center(
                                              child: Row(
                                                children: const [
                                                  Icon(
                                                    Icons.close,
                                                    color: white,
                                                  ),
                                                  Text('Remove',
                                                      style: TextStyle(
                                                          color: white,
                                                          fontWeight:
                                                              FontWeight.w500)),
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          })
                    ],
                  ),
                );
        }
      }),
    );
  }
}
