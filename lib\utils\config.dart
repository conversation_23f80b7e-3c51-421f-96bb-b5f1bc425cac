// ** DB Section ** //
// config.dart
//String authToken = '{"key1": "value1", "key2": "value2"}';  // مثال على JSON string مباشر
String authToken = '{"key1": "value1", "key2": "value2"}'; //  JSON صحيح
String appName = 'FreeDOMS';
String siteUrl =
    'https://freedoms-app.com/app_api.php?type=user_login&application=phone';
//apiKey
String apiKey = 'aec7927aaec0ff835c60b6e296331628';

String appVersion = '1.0';
String helpSupportUrl = '$siteUrl/contact-us';
String privacyPolicyUrl = '$siteUrl/terms/privacy-policy';
int storyDuration = 8; // type second only and it should be int value.
String fontFamily = 'Poppins';
// assets location
// png
String appIcon = 'assets/images/icon.png';
String onBoard1 = 'assets/images/on2.png';
String onBoard2 = 'assets/images/on3.png';
String onBoard3 = 'assets/images/on1.png';
// json
String lockJson = 'assets/json/lock.json';
//svg
String noData = 'assets/svg/no_data.svg';
String likeIcon = 'assets/svg/like.svg';

// String Section
// ** OnBoarding ** //
String onTitle1 = 'Build Community!';
String onDec1 = 'Let\'s Build up your strong community in our platform.';
String onTitle2 = 'Stay connected!';
String onDec2 = 'Always stay connected with your friends and family.';
String onTitle3 = 'Refer And Earn!';
String onDec3 = 'Refer to your friends using your refer link to earn money.';
String okText = 'Ok';
String credentials = 'Please enter your credentials';
String usernameError = '❌ Please enter a username! ❌';
String nameError = '❌ Please enter your full name! ❌';
String passError = '❌ Please enter a password! ❌';
String strongPass = '❌ Please enter a strong password! ❌';
String emailError = '❌ Please enter a valid email! ❌';
String phnError = '❌ Please enter a valid phone number! ❌';
String dobError = '❌ Please enter your DOB! ❌';
String noAcc = 'Don\'t have an account? ';
String alreadyAcc = 'Already have an account? ';
String register = 'Register';
String fillData = 'Please fill all details below';
String registerHere = 'Register Here';
String loginHere = 'Login Here';
String login = 'Login';
String continueText = 'Continue';
String password = 'Password';
String confirmPass = 'Confirm Password';
String username = 'Username';
String email = 'E-mail';
String secureText = '*';
String emptyText = '';
String skip = 'Skip';
String getStart = 'Get Started';
String dataStorated = 'Your data started to our DB';
String succReg = 'Registration Complete';
String somethingWrong = 'Something wrong!';
String invalidDetails = 'Invalid credentials';
String inMind = 'What\'s in your mind?';

//  API  keys
Map<String, dynamic> sd = {
  //  API  keys
  'baseUrl': 'https://freedoms-app.com',
  'skt': 'type',
  'sk': 'user_login',
  'ln':
      'https://freedoms-app.com/app_api.php?type=user_login&application=phone',
  //  ... Add more API keys
};
