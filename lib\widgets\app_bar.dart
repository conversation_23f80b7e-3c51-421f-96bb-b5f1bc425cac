// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import '../components/size_box.dart';
import '../utils/colors.dart';
import '../utils/config.dart';

AppBar customAppBar(double size) {
  return AppBar(
    automaticallyImplyLeading: false,
    backgroundColor: Colors.white,
    elevation: 0,
    title: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Image.asset(
          appIcon,
          color: primary,
          width: 40,
        ),
        Row(
          children: [
            InkWell(
              onTap: (){
                navigate('search');
              },
              child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(.1), shape: BoxShape.circle),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset('assets/svg/search_icon.svg',
                        color: black.withOpacity(.5)),
                  ))
            ),
            sizedBox(0, 15),
            Container(
              height: 40,
              width: 40,
              decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(.1), shape: BoxShape.circle),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: SvgPicture.asset(
                  'assets/svg/notification_icon.svg',
                  color: black.withOpacity(.5),
                ),
              ),
            ),
          ],
        )
      ],
    ),
  );
}
