import 'package:flutter/material.dart';
import 'package:flutter_wowonder/utils/colors.dart';

class CustomButton extends StatelessWidget {
  final String title;
  final void Function()? onTap;
  final Color? bgColor;

  const CustomButton({Key? key, required this.title, this.onTap, this.bgColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return InkWell(
      onTap: onTap,
      child: Container(
        width: size * .9,
        decoration: BoxDecoration(
            color: bgColor ?? primary, borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 15),
          child: Center(
            child: Text(
              title,
              style: const TextStyle(
                color: background,
                letterSpacing: 1,
                fontSize: 16,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
