// ignore_for_file: depend_on_referenced_packages

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_wowonder/components/home/<USER>';
import 'package:flutter_wowonder/controllers/posts/get_group_post_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_user_post_controller.dart';
import 'package:get/get.dart';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/home/<USER>';
import '../components/size_box.dart';
import '../controllers/posts/post_action_controller.dart';
import '../utils/colors.dart';
import 'package:path/path.dart' as path;

class GroupPostSection extends StatefulWidget {
  final String groupID;

  const GroupPostSection({Key? key, required this.groupID}) : super(key: key);

  @override
  State<GroupPostSection> createState() => _GroupPostSectionState();
}

class _GroupPostSectionState extends State<GroupPostSection> {
  final controller = Get.put(GetGroupPostController());
  final PageController pageController = PageController();

  @override
  void initState() {
    controller.getData(groupID: widget.groupID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Get.put(GetUserPostController());
    final postActionController = Get.put(PostActionController());
    final size = MediaQuery.of(context).size.width;
    Get.put(GetUserPostController());
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      } else {
        return controller.dataList.isEmpty
            ? Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [const Text('No post'), sizedBox(10, 0)],
                ),
              )
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15.0),
                child: ListView.builder(
                    padding: EdgeInsets.zero,
                    primary: false,
                    shrinkWrap: true,
                    itemCount: controller.dataList.length,
                    itemBuilder: (_, index) {
                      var data = controller.dataList[index];
                      final fileType = path.extension(data.postFile!);
                      return Column(
                        children: [
                          data.isBoosted == 1
                              ? boostedBadge(size)
                              : Container(),
                          Container(
                            margin: const EdgeInsets.only(bottom: 20),
                            decoration: BoxDecoration(
                                color: white,
                                borderRadius: data.isBoosted == 1
                                    ? const BorderRadius.only(
                                        bottomRight: Radius.circular(20),
                                        bottomLeft: Radius.circular(20),
                                      )
                                    : BorderRadius.circular(20)),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 10),
                              child: Center(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    data.postType == "profile_cover_picture"
                                        ? postType()
                                        : Container(),
                                    //publisher start
                                    mainPublisher(controller.dataList[index],
                                        size, context, postActionController),
                                    //publisher end
                                    data.postFeeling != ''
                                        ? postFeeling(data)
                                        : Container(),
                                    const Divider(),
                                    sizedBox(5, 0),
                                    data.postText == ''
                                        ? Container()
                                        : Html(data: data.postText!),
                                    sizedBox(5, 0),
                                    data.sharedInfo == null
                                        ? Container()
                                        : sharedInfo(data, size),
                                    data.multiImage == '1'
                                        ? multiImage(data, size, pageController)
                                        : Container(),
                                    data.postFile!.isNotEmpty
                                        ? fileType == '.mp4' ||
                                                fileType == '.m4v' ||
                                                fileType == '.webm' ||
                                                fileType == '.flv' ||
                                                fileType == '.mov' ||
                                                fileType == '.mpeg' ||
                                                fileType == '.mkv'
                                            ? AspectRatio(
                                                aspectRatio: 16 / 9,
                                                child: BetterPlayer.network(
                                                  data.postFile!,
                                                  betterPlayerConfiguration:
                                                      const BetterPlayerConfiguration(
                                                    aspectRatio: 16 / 9,
                                                  ),
                                                ),
                                              )
                                            : fileType == '.jpg' ||
                                                    fileType == '.jpeg' ||
                                                    fileType == '.png' ||
                                                    fileType == '.gif'
                                                ? InkWell(
                                                    onTap: () {
                                                      showDialog(
                                                          context: context,
                                                          builder: (_) => AlertDialog(
                                                            insetPadding: EdgeInsets.zero,
                                                            contentPadding: EdgeInsets.zero,
                                                            clipBehavior: Clip.antiAliasWithSaveLayer,
                                                            shape: const RoundedRectangleBorder(
                                                                borderRadius:
                                                                BorderRadius.all(Radius.circular(10.0))),
                                                            content: Builder(builder: (context) {
                                                              return Image.network(data.postFile!,
                                                                fit: BoxFit.cover,
                                                              );
                                                            }),
                                                          ));
                                                    },
                                                    child: Container(
                                                      height: 250,
                                                      width: size * .9,
                                                      decoration: BoxDecoration(
                                                          color: Colors.grey,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(20),
                                                          image:
                                                              DecorationImage(
                                                            image: NetworkImage(
                                                                data.postFile!),
                                                            fit: BoxFit.cover,
                                                          )),
                                                    ),
                                                  )
                                                : Container()
                                        : Container(),
                                    sizedBox(10, 0),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        postMeta(data),
                                        const Divider(),
                                        postAction(
                                            controller, data, index, context),
                                      ],
                                    ),
                                    sizedBox(20, 0),
                                    data.getPostComments!.isNotEmpty
                                        ? postComment(data, context)
                                        : Container(),
                                    data.getPostComments!.isNotEmpty
                                        ? sizedBox(20, 0)
                                        : Container(),
                                    data.isComment == "0"
                                        ? commentInactive(size)
                                        : CommentActive(data),
                                    sizedBox(10, 0),
                                  ],
                                ),
                              ),
                            ),
                          )
                        ],
                      );
                    }),
              );
      }
    });
  }
}
