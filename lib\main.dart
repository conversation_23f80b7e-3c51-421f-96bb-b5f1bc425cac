import 'package:flutter/material.dart' hide Key;
import 'package:flutter_wowonder/utils/config.dart';
import 'package:flutter_wowonder/utils/controller_bindings.dart';
import 'routes/all_routes.dart';
import 'package:get/get.dart';
import 'package:firebase_core/firebase_core.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  ControllerBindings().dependencies();
  await Firebase.initializeApp();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      initialBinding: ControllerBindings(),
      debugShowCheckedModeBanner: false,
      getPages: routes,
      theme: ThemeData(
        fontFamily: fontFamily,
        primarySwatch: Colors.orange,
      ),
      initialRoute: 'login',
    );
  }
}
