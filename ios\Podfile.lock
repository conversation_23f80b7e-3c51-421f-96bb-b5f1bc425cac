PODS:
  - audioplayers_darwin (0.0.1):
    - Flutter
  - better_player (0.0.1):
    - <PERSON>ache (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - Flutter (1.0.0)
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - PINCache (3.0.3):
    - PINCache/Arc-exception-safe (= 3.0.3)
    - PINCache/Core (= 3.0.3)
  - PINCache/Arc-exception-safe (3.0.3):
    - PINCache/Core
  - PINCache/Core (3.0.3):
    - PINOperation (~> 1.2.1)
  - PINOperation (1.2.2)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - TOCropViewController (2.6.1)
  - wakelock (0.0.1):
    - Flutter

DEPENDENCIES:
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - better_player (from `.symlinks/plugins/better_player/ios`)
  - Flutter (from `Flutter`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
    - PINOperation
    - TOCropViewController

EXTERNAL SOURCES:
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  better_player:
    :path: ".symlinks/plugins/better_player/ios"
  Flutter:
    :path: Flutter
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"

SPEC CHECKSUMS:
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  better_player: 2406bfe8175203c7a46fa15f9d778d73b12e1646
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  image_cropper: a3291c624a953049bc6a02e1f8c8ceb162a24b25
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  photo_manager: 4f6810b7dfc4feb03b461ac1a70dacf91fba7604
  PINCache: 7a8fc1a691173d21dbddbf86cd515de6efa55086
  PINOperation: daa34d4aa1d8449089be7d405b9d974abc4724c6
  shared_preferences_foundation: 986fc17f3d3251412d18b0265f9c64113a8c2472
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f

PODFILE CHECKSUM: 542a9c7d9a0b78ac8dc6b785dd41c4bfaaaf6a68

COCOAPODS: 1.11.3
