import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/PostModels/get_saved_post_model.dart';

class SavedPostController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  RxList<SavedDatum> dataList = <SavedDatum>[].obs;

  getData({String? tokenID, String? afterPostID, String? type}) async {
    isLoading.value = true;
    var apiData = await apiServices.getSavedPost();
    dataList.value = apiData!.data!;
    isLoading.value = false;
    update();
  }


  statusChange(String id, String status, SavedDatum getCountries, int index,
      {String? ccc}) async {
    await apiServices.postReaction(postID: id, action: status, count: ccc);
    getCountries.setIsActive(ccc.toString());
    dataList[index] = getCountries;
    update();
  }
  savedStatus(
      {required String id,
      required String status,
      required bool state,
      required SavedDatum getCountries,
      required int index}) async {
    await APISERvices().postAction(postID: id, action: status);
    getCountries.setIsSaved(state);
    dataList[index] = getCountries;
    update();
  }
}
