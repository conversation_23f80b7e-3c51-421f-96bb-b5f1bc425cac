// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/controllers/comments/get_all_comments_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:get/get.dart';
import '../../components/size_box.dart';
import '../../services/api_services.dart';
import '../../widgets/button_widget.dart';
import 'comment_reply_screen.dart';

class PostCommentScreen extends StatefulWidget {
  final String postID;

  const PostCommentScreen({Key? key, required this.postID}) : super(key: key);

  @override
  State<PostCommentScreen> createState() => _PostCommentScreenState();
}

class _PostCommentScreenState extends State<PostCommentScreen> {
  String timeAgo({bool numericDates = false, required DateTime date}) {
    final date2 = DateTime.now();
    final difference = date2.difference(date);
    if ((difference.inDays / 7).floor() >= 1) {
      return 'Last week';
    } else if (difference.inDays >= 2) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays >= 1) {
      return 'Yesterday';
    } else if (difference.inHours >= 2) {
      return '${difference.inHours} hours ago';
    } else if (difference.inHours >= 1) {
      return 'An hour ago';
    } else if (difference.inMinutes >= 2) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inMinutes >= 1) {
      return 'A minute ago';
    } else if (difference.inSeconds >= 3) {
      return '${difference.inSeconds} seconds ago';
    } else {
      return 'Just Now';
    }
  }

  final controller = Get.put(GetAllPostCommentController());
  final textFieldController = TextEditingController();
  final commentEditController = TextEditingController();

  @override
  void initState() {
    super.initState();
    controller.getData(type: 'fetch_comments', postID: widget.postID);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;

    final shimmerSize = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: background,
        centerTitle: true,
        title: Text(
          'Comments',
          style: TextStyle(color: black.withOpacity(0.5)),
        ),
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              Icons.arrow_back_ios_outlined,
              color: black.withOpacity(0.5),
            )),
      ),
      body: Column(
        children: [
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return searchResultShimmer(shimmerSize);
              } else {}
              if (controller.commentData.value!.isNotEmpty) {
                return SingleChildScrollView(
                  child: Column(
                    children: [
                      Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          ListView.builder(
                              shrinkWrap: true,
                              primary: false,
                              itemCount: controller.commentData.value!.length,
                              itemBuilder: (_, index) {
                                final data =
                                    controller.commentData.value![index];
                                var postDate =
                                    DateTime.fromMillisecondsSinceEpoch(
                                        int.parse(data.commentTime!) * 1000);
                                return Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10.0,
                                    vertical: 5,
                                  ),
                                  child: Container(
                                    width: size * .9,
                                    decoration: BoxDecoration(
                                      color: white,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.only(
                                              right: 10,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              children: [
                                                Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    CircleAvatar(
                                                      radius: 15,
                                                      backgroundImage:
                                                          NetworkImage(
                                                              data.pubAvatar!),
                                                    ),
                                                    sizedBox(0, 10),
                                                    Column(
                                                      crossAxisAlignment:
                                                          CrossAxisAlignment
                                                              .start,
                                                      children: [
                                                        Row(
                                                          children: [
                                                            Text(
                                                              data.pubName!,
                                                              style: const TextStyle(
                                                                  color: black,
                                                                  fontSize: 16,
                                                                  fontWeight:
                                                                      FontWeight
                                                                          .w500),
                                                            ),
                                                            data.isVerified ==
                                                                    '1'
                                                                ? SvgPicture
                                                                    .asset(
                                                                    'assets/svg/verify.svg',
                                                                    width: 15,
                                                                  )
                                                                : Container(),
                                                            data.isPro == '1'
                                                                ? Image.asset(
                                                                    'assets/svg/vip.png',
                                                                    width: 15,
                                                                  )
                                                                : Container()
                                                          ],
                                                        ),
                                                        Text(
                                                            timeAgo(
                                                                date: postDate),
                                                            style: TextStyle(
                                                              color: black
                                                                  .withOpacity(
                                                                      .5),
                                                              fontSize: 10,
                                                            ))
                                                      ],
                                                    )
                                                  ],
                                                ),
                                                if (data.isAuthor == false)
                                                  InkWell(
                                                    onTap: () {
                                                      Get.defaultDialog(
                                                          title: 'Report',
                                                          content: const Text(
                                                            'Do you want to report this comment?',
                                                          ),
                                                          textConfirm: 'Yes',
                                                          textCancel: 'No',
                                                          confirmTextColor:
                                                              white,
                                                          cancelTextColor:
                                                              primary,
                                                          onConfirm: () {
                                                            APISERvices()
                                                                .commentReport(
                                                                    commentID: data
                                                                        .commentID!);
                                                            Get.snackbar(
                                                                'Reported',
                                                                'Your report has been submitted to admin successful!',
                                                                snackPosition:
                                                                    SnackPosition
                                                                        .BOTTOM);
                                                            Navigator.pop(
                                                                context);
                                                          });
                                                    },
                                                    child: const Text(
                                                      'Report',
                                                      style: TextStyle(
                                                          color: primary),
                                                    ),
                                                  )
                                                else
                                                  Row(
                                                    children: [
                                                      InkWell(
                                                        onTap: () {
                                                          commentEditController
                                                                  .text =
                                                              data.text!;
                                                          Get.defaultDialog(
                                                            confirmTextColor:
                                                                white,
                                                            cancelTextColor:
                                                                primary,
                                                            title: 'Edit',
                                                            textConfirm: 'Done',
                                                            textCancel:
                                                                'Cancel',
                                                            content: Container(
                                                              width: size * .9,
                                                              decoration:
                                                                  BoxDecoration(
                                                                color: black
                                                                    .withOpacity(
                                                                        .2),
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            20),
                                                              ),
                                                              child: Padding(
                                                                padding: const EdgeInsets
                                                                        .symmetric(
                                                                    vertical:
                                                                        8.0,
                                                                    horizontal:
                                                                        15),
                                                                child: Center(
                                                                  child:
                                                                      TextField(
                                                                    controller:
                                                                        commentEditController,
                                                                    keyboardType:
                                                                        TextInputType
                                                                            .multiline,
                                                                    minLines: 3,
                                                                    maxLines: 6,
                                                                    decoration:
                                                                        const InputDecoration(
                                                                            border:
                                                                                InputBorder.none),
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                            onConfirm: () {
                                                              APISERvices
                                                                  .commentAction(
                                                                type: 'edit',
                                                                commentID: data
                                                                    .commentID,
                                                                commentText:
                                                                    commentEditController
                                                                        .text
                                                                        .toString(),
                                                              );
                                                              controller.getData(
                                                                  type:
                                                                      'fetch_comments',
                                                                  postID: widget
                                                                      .postID);
                                                              Get.back();
                                                              commentEditController
                                                                  .clear();
                                                            },
                                                          );
                                                        },
                                                        child: Icon(
                                                          Icons.edit,
                                                          size: 22,
                                                          color: black
                                                              .withOpacity(.5),
                                                        ),
                                                      ),
                                                      sizedBox(0, 5),
                                                      InkWell(
                                                        onTap: () {
                                                          Get.defaultDialog(
                                                            title: 'Delete',
                                                            content: const Text(
                                                                'Do you really went to delete this comment?'),
                                                            textConfirm: 'Yes',
                                                            textCancel: 'No',
                                                            cancelTextColor:
                                                                primary,
                                                            confirmTextColor:
                                                                white,
                                                            onConfirm: () {
                                                              APISERvices.commentAction(
                                                                  type:
                                                                      'delete',
                                                                  commentID: data
                                                                      .commentID);
                                                              Get.back();
                                                              controller.getData(
                                                                  type:
                                                                      'fetch_comments',
                                                                  postID: widget
                                                                      .postID);
                                                            },
                                                          );
                                                        },
                                                        child: Icon(
                                                          Icons.delete,
                                                          size: 22,
                                                          color: black
                                                              .withOpacity(.5),
                                                        ),
                                                      )
                                                    ],
                                                  )
                                              ],
                                            ),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Text(
                                              data.text!,
                                              textAlign: TextAlign.justify,
                                            ),
                                          ),
                                          const Divider(),
                                          Padding(
                                            padding: const EdgeInsets.all(8.0),
                                            child: Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Row(
                                                  children: [
                                                    InkWell(
                                                      onTap: () {
                                                        if (data.reactType ==
                                                            '') {
                                                          controller.statusChange(data
                                                              .commentID!, 'reaction_comment', data, index, ccc: '1');
                                                        } else {
                                                          controller.statusChange(data
                                                              .commentID!, 'reaction_comment', data, index, ccc: '');
                                                        }
                                                      },
                                                      child: SvgPicture.asset(
                                                        likeIcon,
                                                        color: data.reactType ==
                                                                '1'
                                                            ? primary
                                                            : black.withOpacity(
                                                                .5),
                                                        width: 22,
                                                      ),
                                                    ),
                                                    sizedBox(0, 5),
                                                    Text(
                                                      '${data.reactionCount} reactions',
                                                      style: TextStyle(
                                                          color: black
                                                              .withOpacity(.5),
                                                          fontSize: 13),
                                                    ),
                                                  ],
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                            builder: (_) =>
                                                                PostCommentReplyScreen(
                                                                  commentID: data
                                                                      .commentID!,
                                                                )));
                                                  },
                                                  child: Row(
                                                    children: [
                                                      Text(
                                                        '${data.replayCount} replies',
                                                        style: TextStyle(
                                                            color: black
                                                                .withOpacity(
                                                                    .5),
                                                            fontSize: 13),
                                                      ),
                                                      sizedBox(0, 5),
                                                      SvgPicture.asset(
                                                        'assets/svg/reply.svg',
                                                        color: black
                                                            .withOpacity(.5),
                                                        width: 28,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }),
                        ],
                      ),
                    ],
                  ),
                );
              } else {
                return Center(
                    child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SvgPicture.asset(
                        noData,
                        width: size * .6,
                      ),
                      sizedBox(20, 0),
                      const Text(
                        'No Comments',
                        style: TextStyle(
                            color: black,
                            fontSize: 18,
                            fontWeight: FontWeight.w500),
                      ),
                      sizedBox(20, 0),
                      CustomButton(
                        onTap: () {
                          controller.getData(
                            type: 'fetch_comments',
                            postID: widget.postID,
                          );
                        },
                        title: 'Refresh',
                      )
                    ],
                  ),
                ));
              }
            }),
          ),
          sizedBox(10, 0),
          Container(
            decoration: BoxDecoration(
                color: white,
                border: Border(
                    top: BorderSide(
                  color: black.withOpacity(.1),
                  width: 1.5,
                ))),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              child: Center(
                  child: TextField(
                controller: textFieldController,
                keyboardType: TextInputType.multiline,
                minLines: 1,
                maxLines: 3,
                style: const TextStyle(color: Colors.black),
                decoration: InputDecoration(
                    suffixIcon: InkWell(
                      onTap: () async {
                        APISERvices.commentAction(
                            commentText: textFieldController.text,
                            postID: widget.postID,
                            type: 'create');
                        controller.getData(
                          type: 'fetch_comments',
                          postID: widget.postID,
                        );
                        textFieldController.clear();
                      },
                      child: const Icon(
                        Icons.send,
                        color: Colors.grey,
                      ),
                    ),
                    hintText: 'Type a comment...',
                    hintStyle: const TextStyle(
                      color: Colors.grey,
                    ),
                    border: InputBorder.none),
              )),
            ),
          )
        ],
      ),
    );
  }
}
