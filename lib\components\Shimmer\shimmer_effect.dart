import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';

import '../../utils/colors.dart';
import '../size_box.dart';

Widget postShimmer(Size effect) {
  return Shimmer.fromColors(
    baseColor: Colors.grey,
    highlightColor: Colors.white,
    child: Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
        child: Container(
          height: effect.height * .650,
          decoration: BoxDecoration(
              color: white.withOpacity(.1),
              borderRadius: BorderRadius.circular(20)),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: black.withOpacity(.2),
                    ),
                    sizedBox(0, 10),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          height: 20,
                          width: effect.width * .5,
                          color: black.withOpacity(.2),
                        ),
                        sizedBox(5, 0),
                        Container(
                          height: 20,
                          width: effect.width * .2,
                          color: black.withOpacity(.2),
                        ),
                      ],
                    )
                  ],
                ),
                Divider(
                  color: black.withOpacity(.2),
                ),
                Container(
                  height: 20,
                  width: effect.width * .9,
                  color: black.withOpacity(.2),
                ),
                sizedBox(10, 0),
                Container(
                  height: effect.height * .4,
                  width: effect.width * .9,
                  decoration: BoxDecoration(
                    color: black.withOpacity(.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
                sizedBox(10, 0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CircleAvatar(
                      backgroundColor: black.withOpacity(.2),
                    ),
                    sizedBox(0, 5),
                    Container(
                      height: effect.height * .050,
                      width: effect.width * .5,
                      color: black.withOpacity(.2),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    ),
  );
}

Widget storyShimmer() {
  return Shimmer.fromColors(
    baseColor: Colors.grey,
    highlightColor: Colors.white,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SizedBox(
          height: 90,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: ListView.builder(
                shrinkWrap: true,
                primary: false,
                itemCount: 3,
                scrollDirection: Axis.horizontal,
                itemBuilder: (_, index) {
                  return Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: black.withOpacity(.3),
                        width: 2,
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: Container(
                      decoration: const BoxDecoration(
                          shape: BoxShape.circle, color: Colors.transparent),
                      child: Padding(
                        padding: const EdgeInsets.all(4.0),
                        child: SizedBox(
                          width: 70,
                          child: CircleAvatar(
                            backgroundColor: black.withOpacity(.3),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
          ),
        )
      ],
    ),
  );
}

Widget homeProfileShimmer() {
  return Shimmer.fromColors(
    baseColor: Colors.grey,
    highlightColor: Colors.white,
    child: Container(
      height: 35,
      width: 35,
      decoration:
          BoxDecoration(shape: BoxShape.circle, color: black.withOpacity(.3)),
    ),
  );
}

Widget searchResultShimmer(Size size) {
  return Shimmer.fromColors(
    baseColor: Colors.grey,
    highlightColor: Colors.white,
    child: ListView.builder(
        shrinkWrap: true,
        primary: false,
        itemCount: 15,
        itemBuilder: (_, index) {
          return Column(
            children: [
              ListTile(
                leading: CircleAvatar(
                  backgroundColor: black.withOpacity(.3),
                ),
                title: Container(
                  height: 20,
                  width: size.width * .5,
                  color: black.withOpacity(.2),
                ),
                subtitle: Container(
                  height: 20,
                  width: size.width * .2,
                  color: black.withOpacity(.2),
                ),
              ),
            ],
          );
        }),
  );
}

Widget profileShimmer(Size size) {
  return Shimmer.fromColors(
    baseColor: Colors.grey,
    highlightColor: Colors.white,
    child: SingleChildScrollView(
      child: Column(
        children: [
          Stack(
            clipBehavior: Clip.none,
            alignment: Alignment.bottomCenter,
            children: [
              Container(
                height: 200,
                width: size.width,
                decoration: BoxDecoration(
                    color: black.withOpacity(.2)
                ),
              ),
              Positioned(
                bottom: -80,
                child: Container(
                  height: 150,
                  width: 150,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: background.withOpacity(.4),
                        width: 6,
                      ),
                      color: black.withOpacity(.2)
                  ),
                ),
              )
            ],
          ),
          sizedBox(90, 0),
          Container(
            height: 20,
            width: size.width * .7,
            color: black.withOpacity(.2),
          ),
          sizedBox(10, 0),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 50.0,
              vertical: 10,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      color: black.withOpacity(.2),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
                sizedBox(0, 10),
                Expanded(
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      color: black.withOpacity(.2),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20.0,
              vertical: 10,
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: black.withOpacity(.2),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
                sizedBox(0, 10),
                Expanded(
                  child: Container(
                    height: 50,
                    decoration: BoxDecoration(
                      color: black.withOpacity(.2),
                      borderRadius: BorderRadius.circular(40),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
                horizontal: 20.0, vertical: 10),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.home,
                      color: Colors.grey.withOpacity(0.6),
                    ),
                    sizedBox(0, 10),
                    Container(
                      height: 10,
                      width: 60,
                      color: black.withOpacity(.2),
                    )
                    
                  ],
                ),
                sizedBox(5, 0),
                Row(
                  children: [
                    Icon(
                      Icons.work,
                      color: Colors.grey.withOpacity(0.6),
                    ),
                    sizedBox(0, 10),
                    Container(
                      height: 10,
                      width: 60,
                      color: black.withOpacity(.2),
                    )

                  ],
                ),
                sizedBox(5, 0),
                Row(
                  children: [
                    Icon(
                      Icons.favorite,
                      color: Colors.grey.withOpacity(0.6),
                    ),
                    sizedBox(0, 10),
                    Container(
                      height: 10,
                      width: 60,
                      color: black.withOpacity(.2),
                    )

                  ],
                ),
                sizedBox(5, 0),
                Row(
                  children: [
                    Icon(
                      Icons.more_horiz_rounded,
                      color: Colors.grey.withOpacity(0.6),
                    ),
                    sizedBox(0, 10),
                    Container(
                      height: 10,
                      width: 60,
                      color: black.withOpacity(.2),
                    )

                  ],
                )
                // Row(
                //   children: [
                //     Icon(
                //       Icons.work,
                //       color: Colors.grey.withOpacity(0.6),
                //     ),
                //     sizedBox(0, 10),
                //     data.working == ''
                //         ? Text('None',
                //         style: TextStyle(
                //             color: Colors.grey.withOpacity(0.8)))
                //         : Text(
                //       data.working!,
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     ),
                //   ],
                // ),
                // sizedBox(5, 0),
                // Row(
                //   children: [
                //     Icon(
                //       Icons.favorite,
                //       color: Colors.grey.withOpacity(0.6),
                //     ),
                //     sizedBox(0, 10),
                //     data.relationshipID == '1'
                //         ? Text(
                //       'Single',
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     )
                //         :
                //     data.relationshipID == '2'
                //         ? Text(
                //       'In a relationship',
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     )
                //         :
                //     data.relationshipID == '3'
                //         ? Text(
                //       'Married',
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     )
                //         :
                //     data.relationshipID == '4'
                //         ? Text(
                //       'Engaged',
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     )
                //         : Text(
                //       'None',
                //       style: TextStyle(
                //           color: Colors.grey.withOpacity(0.8)),
                //     ),
                //   ],
                // ),
                // sizedBox(5, 0),
                // InkWell(
                //   onTap: () {
                //     Navigator.push(
                //         context,
                //         MaterialPageRoute(
                //             builder: (_) => ProfileDetailsScreen(data: data,
                //             )));
                //   },
                //   child: Row(
                //     children: [
                //       Icon(
                //         Icons.more_horiz,
                //         color: Colors.grey.withOpacity(0.6),
                //       ),
                //       sizedBox(0, 10),
                //       Text(
                //         'More details about you.',
                //         style: TextStyle(
                //             color: Colors.grey.withOpacity(0.8)),
                //       ),
                //     ],
                //   ),
                // ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
            child: Container(
              height: size.height * .650,
              decoration: BoxDecoration(
                  color: white.withOpacity(.1),
                  borderRadius: BorderRadius.circular(20)),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CircleAvatar(
                          backgroundColor: black.withOpacity(.2),
                        ),
                        sizedBox(0, 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              height: 20,
                              width: size.width * .5,
                              color: black.withOpacity(.2),
                            ),
                            sizedBox(5, 0),
                            Container(
                              height: 20,
                              width: size.width * .2,
                              color: black.withOpacity(.2),
                            ),
                          ],
                        )
                      ],
                    ),
                    Divider(
                      color: black.withOpacity(.2),
                    ),
                    Container(
                      height: 20,
                      width: size.width * .9,
                      color: black.withOpacity(.2),
                    ),
                    sizedBox(10, 0),
                    Container(
                      height: size.height * .4,
                      width: size.width * .9,
                      decoration: BoxDecoration(
                        color: black.withOpacity(.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    sizedBox(10, 0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          backgroundColor: black.withOpacity(.2),
                        ),
                        sizedBox(0, 5),
                        Container(
                          height: size.height * .050,
                          width: size.width * .5,
                          color: black.withOpacity(.2),
                        )
                      ],
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    ),
  );
}
