import 'dart:convert';
import 'package:flutter_wowonder/controllers/auth/pw_token_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_all_post_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../../components/navigation.dart';
import '../../components/response_error_toast.dart';
import '../Story/get_all_stories.dart';

class LoginController extends GetxController {
  static TKController controller = Get.put(TKController());
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();

  Future<void> userLogin(String email, String password) async {
    isLoading.value = true;
    final pref = await SharedPreferences.getInstance();
    final response =
        await http.post(Uri.parse(controller.total['sd'][0]['ln']), body: {
      controller.total['sd'][0]['skt']: controller.total['sd'][0]['sk'],
      'username': email,
      'password': password,
      'device_type': 'phone' //  أضف  device_type
    });
    try {
      if (response.statusCode == 200) {
        isLoading.value = false;
        final jsonData = jsonDecode(response.body);
        if (jsonData['api_status'] != 200) {
          isLoading.value = false;
          responseError(jsonData);
        } else {
          isLoading.value = false;
          pref.setString('token', jsonData['access_token']);
          pref.setString('id', jsonData['user_id']);
          GetAllPostController().getData(tokenID: jsonData['access_token']);
          GetAllStoriesController().getData(tokenID: jsonData['access_token']);
          navigate('home');
        }
      } else {
        isLoading.value = false;
      }
    } catch (e) {
      isLoading.value = false;
    }
  }
}
