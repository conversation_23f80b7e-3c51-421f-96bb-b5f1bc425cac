import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/screens/GroupsScreen/group_details_screen.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';

import '../../components/Shimmer/shimmer_effect.dart';
import '../../components/size_box.dart';
import '../../controllers/Groups/groups_list_controller.dart';
import '../../utils/colors.dart';

class ListGroupsScreen extends StatefulWidget {
  const ListGroupsScreen({Key? key}) : super(key: key);

  @override
  State<ListGroupsScreen> createState() => _ListGroupsScreenState();
}

class _ListGroupsScreenState extends State<ListGroupsScreen>
    with TickerProviderStateMixin {
  final myGroupsController = Get.put(GetMyGroupsController());
  final joinedGroupsController = Get.put(GetJoinedGroupsController());
  final suggestedGroupsController = Get.put(SuggestedGroupsController());
  TabController? tabController;

  @override
  void initState() {
    tabController = TabController(
      initialIndex: 0,
      length: 3,
      vsync: this,
    );
    myGroupsController.getData(type: 'my_groups');
    suggestedGroupsController.getData(type: 'groups',);
    joinedGroupsController.getData(type: 'joined_groups');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(
        title: 'Groups',
        bottom: TabBar(
          isScrollable: false,
          indicatorColor: primary,
          controller: tabController,
          tabs: const [
            Tab(
              child: Text(
                'My Groups',
                style: TextStyle(color: black),
              ),
            ),
            Tab(
              child: Text(
                'Suggested Groups',
                textAlign: TextAlign.center,
                style: TextStyle(color: black),
              ),
            ),
            Tab(
              child: Text(
                'Joined',
                style: TextStyle(
                  color: black,
                ),
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: tabController,
        children: [
          Obx((){
            if(myGroupsController.isLoading.value){
              return searchResultShimmer(size);
            } else{
              return myGroupsController.myGroupData.value!.isEmpty
                ? const Center(
                child: Text('You haven\'t created any groups yet'),
              ) :
                Padding(
                padding: const EdgeInsets.all(10.0),
                child: ListView.builder(
                    shrinkWrap: true,
                    primary: false,
                    itemCount: myGroupsController.myGroupData.value!.length,
                    itemBuilder: (_, index) {
                      final data = myGroupsController.myGroupData.value![index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color: black.withOpacity(.100), blurRadius: 1)
                            ],
                            color: white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15.0,
                              vertical: 8,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 60,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: primary,
                                    borderRadius: BorderRadius.circular(15),
                                    image: data.cover! ==
                                        '$siteUrl/upload/photos/d-cover.jpg  '
                                        ? DecorationImage(
                                      image: NetworkImage(
                                          '$siteUrl/upload/photos/d-cover.jpg'),
                                      fit: BoxFit.cover,
                                    )
                                        : DecorationImage(
                                        image: NetworkImage(data.cover!),
                                        fit: BoxFit.cover),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: CircleAvatar(
                                          radius: 15,
                                          backgroundImage: data.avatar! ==
                                              "$siteUrl/upload/photos/d-group.jpg "
                                              ? NetworkImage(
                                              '$siteUrl/upload/photos/d-group.jpg')
                                              : NetworkImage(data.avatar!),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                sizedBox(0, 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          data.name!,
                                          style: const TextStyle(
                                              color: black,
                                              fontSize: 19,
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Row(
                                      children: [
                                        data.privacy == '1'
                                            ? const Text('Public •')
                                            : const Text('Private •'),
                                        sizedBox(0, 5),
                                        Text('${data.membersCount} members'),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Text(
                                      data.category!,
                                      style:
                                      TextStyle(color: black.withOpacity(.5)),
                                    ),
                                    sizedBox(3, 0),
                                    InkWell(
                                      onTap: (){
                                        pageRoute(context, GroupDetailsScreen(groupID: data.groupId!));
                                      },
                                      child: Container(
                                        width: size.width * .650,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                            BorderRadius.circular(10),
                                            color: primary),
                                        child: const Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 10.0),
                                          child: Center(
                                            child: Text(
                                              'Visit Group',
                                              style: TextStyle(color: white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
              );
            }
          }),
          Obx((){
            if(suggestedGroupsController.isLoading.value){
              return searchResultShimmer(size);
            } else{
              return suggestedGroupsController.recommendedData.value!.isEmpty
                ? const Center(
                child: Text('No suggested groups.'),
              ) :
                Padding(
                padding: const EdgeInsets.all(10.0),
                child: ListView.builder(
                    shrinkWrap: true,
                    primary: false,
                    itemCount: suggestedGroupsController.recommendedData.value!.length,
                    itemBuilder: (_, index) {
                      final data = suggestedGroupsController.recommendedData.value![index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color: black.withOpacity(.100), blurRadius: 1)
                            ],
                            color: white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15.0,
                              vertical: 8,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 60,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: primary,
                                    borderRadius: BorderRadius.circular(15),
                                    image: data.cover! ==
                                        '$siteUrl/upload/photos/d-cover.jpg  '
                                        ? DecorationImage(
                                      image: NetworkImage(
                                          '$siteUrl/upload/photos/d-cover.jpg'),
                                      fit: BoxFit.cover,
                                    )
                                        : DecorationImage(
                                        image: NetworkImage(data.cover!),
                                        fit: BoxFit.cover),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: CircleAvatar(
                                          radius: 15,
                                          backgroundImage: data.avatar! ==
                                              "$siteUrl/upload/photos/d-group.jpg "
                                              ? NetworkImage(
                                              '$siteUrl/upload/photos/d-group.jpg')
                                              : NetworkImage(data.avatar!),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                sizedBox(0, 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          data.name!,
                                          style: const TextStyle(
                                              color: black,
                                              fontSize: 19,
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Row(
                                      children: [
                                        data.privacy == '1'
                                            ? const Text('Public •')
                                            : const Text('Private •'),
                                        sizedBox(0, 5),
                                        Text('${data.membersCount} members'),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Text(
                                      data.category!,
                                      style:
                                      TextStyle(color: black.withOpacity(.5)),
                                    ),
                                    sizedBox(3, 0),
                                    InkWell(
                                      onTap: (){
                                        pageRoute(context, GroupDetailsScreen(groupID: data.groupId!));
                                      },
                                      child: Container(
                                        width: size.width * .650,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                            BorderRadius.circular(10),
                                            color: primary),
                                        child: const Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 10.0),
                                          child: Center(
                                            child: Text(
                                              'Visit Group',
                                              style: TextStyle(color: white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
              );
            }
          }),
          Obx((){
            if(joinedGroupsController.isLoading.value){
              return searchResultShimmer(size);
            } else{
              return joinedGroupsController.joinedGroupsData.value!.isEmpty
                ? const Center(
                child: Text('No suggested groups.'),
              ) :
                Padding(
                padding: const EdgeInsets.all(10.0),
                child: ListView.builder(
                    shrinkWrap: true,
                    primary: false,
                    itemCount: joinedGroupsController.joinedGroupsData.value!.length,
                    itemBuilder: (_, index) {
                      final data = joinedGroupsController.joinedGroupsData.value![index];
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color: black.withOpacity(.100), blurRadius: 1)
                            ],
                            color: white,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15.0,
                              vertical: 8,
                            ),
                            child: Row(
                              children: [
                                Container(
                                  height: 60,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    color: primary,
                                    borderRadius: BorderRadius.circular(15),
                                    image: data.cover! ==
                                        '$siteUrl/upload/photos/d-cover.jpg  '
                                        ? DecorationImage(
                                      image: NetworkImage(
                                          '$siteUrl/upload/photos/d-cover.jpg'),
                                      fit: BoxFit.cover,
                                    )
                                        : DecorationImage(
                                        image: NetworkImage(data.cover!),
                                        fit: BoxFit.cover),
                                  ),
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Padding(
                                        padding: const EdgeInsets.all(2.0),
                                        child: CircleAvatar(
                                          radius: 15,
                                          backgroundImage: data.avatar! ==
                                              "$siteUrl/upload/photos/d-group.jpg "
                                              ? NetworkImage(
                                              '$siteUrl/upload/photos/d-group.jpg')
                                              : NetworkImage(data.avatar!),
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                sizedBox(0, 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          data.name!,
                                          style: const TextStyle(
                                              color: black,
                                              fontSize: 19,
                                              fontWeight: FontWeight.w400),
                                        ),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Row(
                                      children: [
                                        data.privacy == '1'
                                            ? const Text('Public •')
                                            : const Text('Private •'),
                                        sizedBox(0, 5),
                                        Text('${data.membersCount} members'),
                                      ],
                                    ),
                                    sizedBox(3, 0),
                                    Text(
                                      data.category!,
                                      style:
                                      TextStyle(color: black.withOpacity(.5)),
                                    ),
                                    sizedBox(3, 0),
                                    InkWell(
                                      onTap: (){
                                        pageRoute(context, GroupDetailsScreen(groupID: data.groupId!));
                                      },
                                      child: Container(
                                        width: size.width * .650,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                            BorderRadius.circular(10),
                                            color: primary),
                                        child: const Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 10.0),
                                          child: Center(
                                            child: Text(
                                              'Visit Group',
                                              style: TextStyle(color: white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    }),
              );
            }
          }),
        ],
      ),
    );
  }
}
