// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/size_box.dart';

import '../utils/colors.dart';

class MenuOptionItem extends StatelessWidget {
  final String icon;
  final String title;
  final Color? iconColor;
  final double? iconSize;
  final void Function()? onTap;

  const MenuOptionItem(
      {Key? key,
      required this.icon,
      required this.title,
      this.iconColor,
      this.iconSize, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: background,
            border: Border.all(color: Colors.grey.withOpacity(0.1)),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SvgPicture.asset(
                  icon,
                  width: iconSize ?? 18,
                  color: iconColor ?? primary,
                ),
                sizedBox(0, 5),
                Text(
                  title,
                  style: TextStyle(
                    color: black.withOpacity(0.5),
                    fontSize: 14,
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class LongMenuItems extends StatelessWidget {
  final Color? backgroundColor;
  final Color? borderColor;
  final String icon;
  final double? iconSize;
  final Color? iconColor;
  final String title;
  final void Function()? onTap;
  const LongMenuItems({
    Key? key,
    this.backgroundColor,
    this.borderColor,
    required this.icon,
    this.iconSize,
    required this.title,
    this.iconColor, this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return InkWell(
      onTap: onTap,
      child: Container(
        width: size,
        decoration: BoxDecoration(
          border: Border.all(color: borderColor ?? Colors.grey.withOpacity(0.1)),
          color: backgroundColor ?? background,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0),
              child: Row(
                children: [
                  SvgPicture.asset(
                    icon,
                    color: iconColor ?? primary,
                    width: iconSize ?? 18,
                  ),
                  sizedBox(0, 6),
                  Text(
                    title,
                    style: TextStyle(color: black.withOpacity(0.5)),
                  )
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
