// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../components/profile/item_model.dart';
import '../../models/UserData/get_user_details_model.dart';

class ProfileDetailsScreen extends StatefulWidget {
  final UserData data;

  const ProfileDetailsScreen({Key? key, required this.data}) : super(key: key);

  @override
  State<ProfileDetailsScreen> createState() => _ProfileDetailsScreenState();
}

class _ProfileDetailsScreenState extends State<ProfileDetailsScreen> {

  String? myID = '';
  myIDFinder() async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    myID = id;
    setState(() {

    });
  }

  @override
  void initState() {
    myIDFinder();
    super.initState();
  }
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery
        .of(context)
        .size
        .width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'About'),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: size * .9,
                decoration: BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svg/infoo.svg',
                                width: 26,
                                color: black.withOpacity(.5),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Personal Information',
                                style: TextStyle(
                                    color: black.withOpacity(.5),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          widget.data.userId == myID ? InkWell(
                            onTap: (){
                              navigate('generalSetting');
                            },
                            child: Text('Edit',
                            style: TextStyle(
                              color: black.withOpacity(.5),
                              fontWeight: FontWeight.w500
                            ),),
                          ) : Container()
                        ],
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Name', value: widget.data.name!),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Phone Number', value: widget.data.phnNumber!.isEmpty ? 'None' : widget.data.phnNumber!
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Email Address', value: widget.data.email!.isEmpty ? 'None' : widget.data.email!
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Date Of Birth', value: widget.data.birthday! == '0000-00-00' ? 'None' : widget.data.birthday!
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Gender', value: widget.data.gender!.isEmpty ? 'None' : widget.data.gender!
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Location', value: widget.data.address!.isEmpty ? 'None' : widget.data.address!
                      ),
                    ],
                  ),
                ),
              ),
              sizedBox(20, 0),
              Container(
                width: size * .9,
                decoration: BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svg/education.svg',
                                width: 26,
                                color: black.withOpacity(.5),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Life Event',
                                style: TextStyle(
                                    color: black.withOpacity(.5),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          widget.data.userId == myID ? InkWell(
                            onTap: (){
                              navigate('lifeEventSetting');
                            },
                            child: Text('Edit',
                              style: TextStyle(
                                  color: black.withOpacity(.5),
                                  fontWeight: FontWeight.w500
                              ),),
                          ) : Container()
                        ],
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(
                          schoolCompleted: widget.data.schoolCompleted,
                          size: size,
                          title: 'School',
                          value: widget.data.school!.isEmpty ? 'None' : widget.data.school!
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(title: 'Working', value: widget.data.working!.isEmpty ? 'None' : widget.data.working!),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(
                          title: 'Company Website',
                          value: widget.data.companyWebsite!.isEmpty ? 'None' : widget.data.companyWebsite!),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      infoItem(
                          title: 'Relationship',
                          value: widget.data.relationshipID == '1'
                              ? 'Single'
                              : widget.data.relationshipID == '2'
                              ? 'In a relationship'
                              : widget.data.relationshipID == '3'
                              ? 'Married'
                              : widget.data.relationshipID == '4'
                              ? 'Engaged'
                              : 'None',
                      ),
                    ],
                  ),
                ),
              ),
              sizedBox(20, 0),
              Container(
                width: size * .9,
                decoration: BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svg/network.svg',
                                width: 26,
                                color: black.withOpacity(.5),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Networking',
                                style: TextStyle(
                                    color: black.withOpacity(.5),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          widget.data.userId == myID ? InkWell(
                            onTap: (){
                              navigate('networkingSetting');
                            },
                            child: Text('Edit',
                              style: TextStyle(
                                  color: black.withOpacity(.5),
                                  fontWeight: FontWeight.w500
                              ),),
                          ) : Container()
                        ],
                      ),
                      widget.data.facebook!.isEmpty && widget.data.twitter!.isEmpty &&
                          widget.data.linkedin!.isEmpty && widget.data.youtube!.isEmpty &&
                          widget.data.vk!.isEmpty && widget.data.instagram!.isEmpty ?
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Divider(
                                color: black.withOpacity(.1),
                              ),
                              const Text('No Data'),
                              Divider(
                                color: black.withOpacity(.1),
                              ),
                            ],
                          ):
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.facebook!.isEmpty ? Container() : infoItem(
                              size: size, title: 'Facebook', value: widget.data.facebook!),
                          widget.data.twitter!.isEmpty ? Container() : Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.twitter!.isEmpty ? Container() : infoItem(
                              size: size, title: 'Twitter', value: widget.data.twitter!),
                          widget.data.linkedin!.isEmpty ? Container() : Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.linkedin!.isEmpty ? Container() : infoItem(
                              size: size, title: 'Linkedin', value: widget.data.linkedin!),
                          widget.data.youtube!.isEmpty ? Container() : Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.youtube!.isEmpty ? Container() : infoItem(
                              size: size, title: 'YouTube', value: widget.data.youtube!),
                          widget.data.vk!.isEmpty ? Container() : Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.vk!.isEmpty ? Container() : infoItem(
                              size: size, title: 'VK', value: widget.data.vk!),
                          widget.data.instagram!.isEmpty ? Container() : Divider(
                            color: black.withOpacity(.1),
                          ),
                          widget.data.instagram!.isEmpty ? Container() : infoItem(
                              size: size, title: 'Instagram', value: widget.data.instagram!),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              sizedBox(20, 0),
              Container(
                width: size * .9,
                decoration: BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svg/aboutSection.svg',
                                width: 26,
                                color: black.withOpacity(.5),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'About',
                                style: TextStyle(
                                    color: black.withOpacity(.5),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          widget.data.userId == myID ? InkWell(
                            onTap: (){
                              navigate('aboutSetting');
                            },
                            child: Text('Edit',
                              style: TextStyle(
                                  color: black.withOpacity(.5),
                                  fontWeight: FontWeight.w500
                              ),),
                          ) : Container()
                        ],
                      ),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                      Text(widget.data.about == null ? 'No Data' : widget.data.about!),
                      Divider(
                        color: black.withOpacity(.1),
                      ),
                    ],
                  ),
                ),
              ),
              sizedBox(20, 0),

            ],
          ),
        ),
      ),
    );
  }
}
