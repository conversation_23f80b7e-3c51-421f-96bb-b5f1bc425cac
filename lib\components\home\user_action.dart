import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/posts/get_all_post_controller.dart';
import '../../utils/colors.dart';
import '../size_box.dart';

Widget userAction(context, data, size, controller) {
  final allController = Get.put(GetAllPostController());
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.end,
    children: [
      InkWell(
        onTap: () {
          showModalBottomSheet(
              backgroundColor: Colors.transparent,
              context: context,
              builder: (_) {
                return Container(
                  height: MediaQuery.of(context).size.height * .220,
                  width: size,
                  decoration: const BoxDecoration(
                      color: white,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20))),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 10.0, vertical: 15),
                    child: Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          sizedBox(20, 0),
                          SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                sizedBox(0, 20),
                                data.isAuthor == true
                                    ? InkWell(
                                        onTap: () {
                                          Get.defaultDialog(
                                              title: 'Are you sure?',
                                              content: const Text(
                                                'Really you want to delete this post from your profile?',
                                              ),
                                              textConfirm: 'Yes',
                                              cancelTextColor: primary,
                                              confirmTextColor: white,
                                              textCancel: 'No',
                                              onCancel: () {
                                                Navigator.pop(context);
                                              },
                                              onConfirm: () {
                                                try {
                                                  controller.postAction(
                                                      postID: data.postId!,
                                                      action: 'delete');
                                                } catch (e) {
                                                  return;
                                                }
                                                allController.onRefresh();
                                                Get.back();
                                                Get.back();
                                              });
                                        },
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              height: 60,
                                              width: 60,
                                              decoration: BoxDecoration(
                                                color: Colors.redAccent,
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: const Center(
                                                child: Icon(Icons.delete,
                                                    color: white),
                                              ),
                                            ),
                                            sizedBox(5, 0),
                                            const Text(
                                              'Delete\nPost',
                                              textAlign: TextAlign.center,
                                            )
                                          ],
                                        ),
                                      )
                                    : Container(),
                                data.isAuthor == true
                                    ? sizedBox(0, 10)
                                    : Container(),
                                data.isAuthor == true
                                    ? InkWell(
                                        onTap: () {
                                          Get.defaultDialog(
                                              title: 'Are you sure?',
                                              content: const Text(
                                                'Really you want to disable comment for this post?',
                                              ),
                                              textConfirm: 'Yes',
                                              cancelTextColor: primary,
                                              confirmTextColor: white,
                                              textCancel: 'No',
                                              onCancel: () {
                                                Navigator.pop(context);
                                              },
                                              onConfirm: () {
                                                try {
                                                  controller.postAction(
                                                      postID: data.postId!,
                                                      action:
                                                          'disable_comments');
                                                } catch (e) {
                                                  return;
                                                }
                                                allController.onRefresh();
                                                Get.back();
                                                Get.back();
                                              });
                                        },
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              height: 60,
                                              width: 60,
                                              decoration: BoxDecoration(
                                                color: Colors.redAccent,
                                                borderRadius:
                                                    BorderRadius.circular(10),
                                              ),
                                              child: const Center(
                                                child: Icon(
                                                  Icons.comments_disabled,
                                                  color: white,
                                                ),
                                              ),
                                            ),
                                            sizedBox(5, 0),
                                            const Text(
                                              'Disable\nComment',
                                              textAlign: TextAlign.center,
                                            )
                                          ],
                                        ),
                                      )
                                    : Container(),
                                data.isAuthor == true
                                    ? sizedBox(0, 10)
                                    : Container(),
                                InkWell(
                                  onTap: () {
                                    try {
                                      controller.postAction(
                                          postID: data.postId!,
                                          action: 'report');
                                    } catch (e) {
                                      return;
                                    }
                                    Future.delayed(const Duration(seconds: 1),
                                        () {
                                      Navigator.pop(context);
                                    });
                                  },
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        height: 60,
                                        width: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.redAccent,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: const Center(
                                          child: Icon(
                                            Icons.report_outlined,
                                            color: white,
                                          ),
                                        ),
                                      ),
                                      sizedBox(5, 0),
                                      const Text('Report')
                                    ],
                                  ),
                                ),
                                sizedBox(0, 10),
                                InkWell(
                                  onTap: () {
                                    try {
                                      controller.postAction(
                                          postID: data.postId!, action: 'save');
                                    } catch (e) {
                                      return;
                                    }
                                    Future.delayed(const Duration(seconds: 1),
                                        () {
                                      Navigator.pop(context);
                                    });
                                  },
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        height: 60,
                                        width: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.redAccent,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: const Center(
                                          child: Icon(
                                            Icons.bookmark_outline,
                                            color: white,
                                          ),
                                        ),
                                      ),
                                      sizedBox(5, 0),
                                      const Text('Save')
                                    ],
                                  ),
                                ),
                                sizedBox(0, 10),
                                InkWell(
                                  onTap: () {
                                    Get.defaultDialog(
                                        title: 'Be Careful!',
                                        content: const Text(
                                          'If you hide this post from your profile you can\'t able to show this post again',
                                        ),
                                        textConfirm: 'Hide',
                                        cancelTextColor: primary,
                                        confirmTextColor: white,
                                        textCancel: 'Cancel',
                                        onCancel: () {
                                          Navigator.pop(context);
                                        },
                                        onConfirm: () {
                                          try {
                                            controller.hidePost(
                                                postID: data.postId!);
                                          } catch (e) {
                                            return;
                                          }
                                          allController.onRefresh();
                                          Get.back();
                                          Get.back();
                                        });
                                  },
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        height: 60,
                                        width: 60,
                                        decoration: BoxDecoration(
                                          color: Colors.redAccent,
                                          borderRadius:
                                              BorderRadius.circular(10),
                                        ),
                                        child: const Center(
                                          child: Icon(
                                            Icons.hide_source,
                                            color: white,
                                          ),
                                        ),
                                      ),
                                      sizedBox(5, 0),
                                      const Text('Hide Post')
                                    ],
                                  ),
                                ),
                                sizedBox(0, 20),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                );
              });
        },
        child: const Center(
          child: Padding(
            padding: EdgeInsets.all(5.0),
            child: Icon(
              Icons.more_vert,
              size: 18,
            ),
          ),
        ),
      ),
    ],
  );
}
