import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import '../utils/colors.dart';
import '../utils/config.dart';

Future<dynamic> responseError(jsonData) {
  return Get.defaultDialog(
    backgroundColor: Colors.white,
    title: jsonData['errors']['error_text'],
    titleStyle: const TextStyle(
      color: primary,
    ),
    content: SvgPicture.asset(
      'assets/svg/error.svg',
      width: 150,
    ),
    actions: [
      Container(
        decoration: BoxDecoration(
            border: Border.all(color: primary),
            borderRadius: BorderRadius.circular(15)),
        child: TextButton(
          onPressed: () {
            Get.back();
          },
          child: Text(
            okText,
            style: const TextStyle(color: primary),
          ),
        ),
      )
    ],
  );
}
