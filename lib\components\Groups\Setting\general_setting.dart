import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/Groups/group_details_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:flutter_wowonder/widgets/form_helper.dart';
import 'package:get/get.dart';
import '../../../utils/colors.dart';

class GroupGeneralSetting extends StatelessWidget {
  final String groupID;

  const GroupGeneralSetting({Key? key, required this.groupID})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final data = Get.put(GroupDetailsController());
    final pageData = data.groupData.value;
    final pageNameController = TextEditingController();
    final aboutController = TextEditingController();
    final userNameController = TextEditingController();
    pageNameController.text = pageData.name!;
    userNameController.text = pageData.username!;
    aboutController.text = pageData.about!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'General'),
      body: Obx((){
        if(data.isLoading.value){
          return searchResultShimmer(size);
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  sizedBox(10, 0),
                  sizedBox(20, 0),
                  const Text(
                    'Basic Details',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  sizedBox(10, 0),
                  Form(
                      child: Column(
                        children: [
                          customFieldHelper(
                              controller: pageNameController,
                              prefixIcon: Icons.person,
                              label: 'Group Name'),
                          sizedBox(15, 0),
                          customFieldHelper(
                            controller: userNameController,
                            prefixIcon: Icons.alternate_email,
                            label: 'Group Username',
                          ),
                        ],
                      )),
                  sizedBox(15, 0),
                  Container(
                    decoration: BoxDecoration(
                        border: Border.all(
                          color: black.withOpacity(.1),
                        ),
                        borderRadius: BorderRadius.circular(15)),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 15.0),
                      child: TextField(
                        controller: aboutController,
                        keyboardType: TextInputType.multiline,
                        minLines: 1,
                        maxLines: 10,
                        decoration: const InputDecoration(
                            border: InputBorder.none,
                            label: Text('About'),
                            hintText: 'Describe about this groups!'),
                      ),
                    ),
                  ),
                  sizedBox(15, 0),
                  CustomButton(
                    title: 'Save',
                    onTap: () async {
                      APISERvices.updateGroupGeneral(
                        groupID: groupID,
                        context: context,
                        groupName: pageNameController.text,
                        userName: userNameController.text,
                        about: aboutController.text,
                      );
                    },
                  )
                ],
              ),
            ),
          );
        }
      }),
    );
  }
}
