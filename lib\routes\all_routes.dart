import 'package:flutter_wowonder/screens/AddNewPost/add_new_post.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/followers_screen.dart';
import 'package:get/get.dart';
import '../components/Settings/about_setting.dart';
import '../components/Settings/general_setting.dart';
import '../components/Settings/life_event_setting.dart';
import '../components/Settings/networking_setting.dart';
import '../components/Settings/privacy_setting.dart';
import '../components/Settings/security_setting.dart';
import '../screens/AccountDeleteScreen/account_delete_screen.dart';
import '../screens/Auth/ForgotPassword/confirmation_code_screen.dart';
import '../screens/Auth/ForgotPassword/forget_password.dart';
import '../screens/Auth/LoginScreen/login_screen.dart';
import '../screens/Auth/SignUpScreen/register_screen.dart';
import '../screens/ErrorScreen/error_screen.dart';
import '../screens/GroupsScreen/list_groups_screen.dart';
import '../screens/HomeSceen/home_screen.dart';
import '../screens/MoreScreen/more_option.dart';
import '../screens/OnBoarding/onboarding_screen.dart';
import '../screens/PagesScreen/list_pages_screen.dart';
import '../screens/SavedPostScreen/saved_post_screen.dart';
import '../screens/SearchScreen/search_result_screen.dart';
import '../screens/SearchScreen/search_screen.dart';
import '../screens/SettingsScreen/settings_screen.dart';
import '../screens/SplashScreen/splash_screen.dart';
import '../screens/VerifyRequest/request_sent_success_screen.dart';
import '../screens/VerifyRequest/verification_req_screen.dart';

List<GetPage<dynamic>>? routes = [
  GetPage(name: '/', page: () => const OnBoarding()),
  GetPage(
      name: '/login',
      page: () => const LoginScreen(),
      transition: Transition.native),
  GetPage(name: '/register', page: () => const RegisterScreen()),
  GetPage(name: '/home', page: () => const HomeScreen()),
  GetPage(name: '/more', page: () => const MoreOptionScreen()),
  GetPage(name: '/userFollowers', page: () => const UserFollowerScreen()),
  GetPage(name: '/newPost', page: () => const NewPostScreen()),
  GetPage(name: '/forgot', page: () => const ForgetPassScreen()),
  GetPage(name: '/resetCode', page: () => const ResetCodeScreen()),
  GetPage(name: '/search', page: () => const SearchScreen()),
  GetPage(name: '/searchResults', page: () => const SearchResultScreen()),
  GetPage(name: '/splash', page: () => const SplashScreen()),
  GetPage(name: '/error', page: () => const ErrorScreen()),
  GetPage(name: '/getVerified', page: () => const VerifyRequestScreen()),
  GetPage(name: '/verifyReqSuccess', page: () => const RequestSentSuccess()),
  GetPage(name: '/pageList', page: () => const PageListScreen()),
  GetPage(name: '/saved', page: () => const SavedPostScreen()),
  GetPage(name: '/groupList', page: () => const ListGroupsScreen()),
  GetPage(name: '/settings', page: () => const SettingScreen()),
  GetPage(name: '/generalSetting', page: () => const GeneralSetting()),
  GetPage(name: '/securitySetting', page: () => const SecuritySetting()),
  GetPage(name: '/privacySetting', page: () => const PrivacySetting()),
  GetPage(name: '/lifeEventSetting', page: () => const LifeEvent()),
  GetPage(name: '/networkingSetting', page: () => const NetworkingSetting()),
  GetPage(name: '/aboutSetting', page: () => const AboutSetting()),
  GetPage(name: '/accountDelete', page: () => const AccountDeleteScreen()),
];
