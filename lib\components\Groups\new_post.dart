import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/controllers/Groups/group_details_controller.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:get/get.dart';
import 'package:media_picker_widget/media_picker_widget.dart';
import '../../components/size_box.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';

// ignore: depend_on_referenced_packages
import 'package:path/path.dart' as path;

class NewGroupPost extends StatefulWidget {
  final String groupID;
  final String name;
  final String avatar;

  const NewGroupPost(
      {Key? key,
      required this.groupID,
      required this.name,
      required this.avatar})
      : super(key: key);

  @override
  State<NewGroupPost> createState() => _NewGroupPostState();
}

class _NewGroupPostState extends State<NewGroupPost> {
  List<Media> mediaList = [];
  List<Media> selectedMedia = [];
  final postData = TextEditingController();

  void openImagePicker(BuildContext context, bool isVideo) {
    showModalBottomSheet(
        context: context,
        builder: (context) {
          return MediaPicker(
            mediaList: mediaList,
            onPicked: (selectedList) {
              selectedMedia.addAll(selectedList);
              setState(() {});
              Navigator.pop(context);
            },
            onCancel: () {},
            mediaCount: MediaCount.multiple,
            mediaType: isVideo ? MediaType.video : MediaType.all,
            decoration: PickerDecoration(
              loadingWidget: const CircularProgressIndicator(),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: background,
      appBar: PreferredSize(
          preferredSize: const Size.fromHeight(80),
          child: SafeArea(
            child: Column(
              children: [
                AppBar(
                  elevation: 1,
                  shadowColor: black.withOpacity(0.2),
                  backgroundColor: background,
                  leading: IconButton(
                      onPressed: () {
                        Get.back();
                      },
                      icon: const Icon(
                        Icons.close,
                        color: black,
                      )),
                  title: Text(
                    'Create New Post',
                    style: TextStyle(
                        color: black.withOpacity(0.8),
                        fontWeight: FontWeight.w600),
                  ),
                  centerTitle: true,
                  actions: [
                    TextButton(
                        onPressed: () async {
                          if (selectedMedia.isEmpty) {
                            APISERvices().addNewPost(
                              postText: postData.text,
                              privacy: '0',
                              userID: '',
                              groupID: widget.groupID,
                            );
                            GroupDetailsController()
                                .getData(pageID: widget.groupID);
                            Get.back();
                          } else {
                            APISERvices().addNewPost(
                              postText: postData.text,
                              privacy: '0',
                              userID: '',
                              mediaList: selectedMedia,
                              groupID: widget.groupID,
                            );
                            GroupDetailsController()
                                .getData(pageID: widget.groupID);
                          }
                        },
                        child: const Text(
                          'Post',
                          style: TextStyle(
                            color: primary,
                            fontSize: 18,
                          ),
                        ))
                  ],
                ),
              ],
            ),
          )),
      body: SingleChildScrollView(
        child: Column(
          children: [
            sizedBox(10, 0),
            Center(
              child: SingleChildScrollView(
                padding: EdgeInsets.zero,
                physics: const BouncingScrollPhysics(),
                child: Container(
                  width: size.width * .9,
                  decoration: BoxDecoration(
                      color: white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(color: black.withOpacity(0.1), blurRadius: 5)
                      ]),
                  child: Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          leading: CircleAvatar(
                            radius: 25,
                            backgroundImage: widget.avatar ==
                                    '$siteUrl/upload/photos/d-group.jpg '
                                ? NetworkImage(
                                    '$siteUrl/upload/photos/d-group.jpg')
                                : NetworkImage(widget.avatar),
                          ),
                          title: Text(
                            widget.name,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          subtitle: Row(
                            children: [
                              SvgPicture.asset(
                                'assets/svg/global2.svg',
                                // ignore: deprecated_member_use
                                color: black.withOpacity(0.5),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Public',
                                style: TextStyle(color: black.withOpacity(0.5)),
                              ),
                              sizedBox(0, 5),
                              const Icon(
                                Icons.arrow_drop_down_circle_outlined,
                                size: 15,
                              )
                            ],
                          ),
                        ),
                        Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: background),
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: TextFormField(
                              validator: (value) {
                                if (value!.isEmpty) {
                                  Get.snackbar('Something Wrong',
                                      'Please add some values',
                                      snackPosition: SnackPosition.BOTTOM);
                                  return '';
                                }
                                return null;
                              },
                              controller: postData,
                              textAlign: TextAlign.justify,
                              maxLines: 10,
                              minLines: 10,
                              decoration: const InputDecoration(
                                  border: InputBorder.none),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        SizedBox(
                          height: 100,
                          child: ListView(
                            physics: const BouncingScrollPhysics(),
                            scrollDirection: Axis.horizontal,
                            children: [
                              InkWell(
                                onTap: () {
                                  Get.bottomSheet(
                                    Container(
                                      height: size.height * .130,
                                      width: size.width,
                                      decoration: const BoxDecoration(
                                          color: white,
                                          borderRadius: BorderRadius.only(
                                            topRight: Radius.circular(20),
                                            topLeft: Radius.circular(20),
                                          )),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            vertical: 10.0, horizontal: 30),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceEvenly,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            InkWell(
                                              onTap: () {
                                                openImagePicker(context, false);
                                              },
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    height: 45,
                                                    width: 45,
                                                    decoration:
                                                        const BoxDecoration(
                                                            color: Colors.red,
                                                            shape: BoxShape
                                                                .circle),
                                                    child: const Padding(
                                                      padding:
                                                          EdgeInsets.all(4.0),
                                                      child: Icon(
                                                        Icons.photo,
                                                        color: white,
                                                      ),
                                                    ),
                                                  ),
                                                  sizedBox(5, 0),
                                                  const Text('Photos')
                                                ],
                                              ),
                                            ),
                                            InkWell(
                                              onTap: () {
                                                openImagePicker(context, true);
                                              },
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    height: 45,
                                                    width: 45,
                                                    decoration:
                                                        const BoxDecoration(
                                                            color: Colors.green,
                                                            shape: BoxShape
                                                                .circle),
                                                    child: const Padding(
                                                      padding:
                                                          EdgeInsets.all(4.0),
                                                      child: Icon(
                                                        Icons.video_call,
                                                        color: white,
                                                      ),
                                                    ),
                                                  ),
                                                  sizedBox(5, 0),
                                                  const Text('Videos')
                                                ],
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                                child: Container(
                                  height: 90,
                                  width: 80,
                                  decoration: BoxDecoration(
                                      color: background,
                                      borderRadius: BorderRadius.circular(15),
                                      border: Border.all(
                                        color: black.withOpacity(.2),
                                        width: .5,
                                      )),
                                  child: Center(
                                    child: Icon(
                                      Icons.add,
                                      color: black.withOpacity(.2),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(
                                height: 100,
                                child: ListView.builder(
                                    padding: EdgeInsets.zero,
                                    scrollDirection: Axis.horizontal,
                                    shrinkWrap: true,
                                    primary: false,
                                    itemCount: selectedMedia.length,
                                    itemBuilder: (_, index) {
                                      final fileType = path.extension(
                                          selectedMedia[index].file!.path);
                                      return Padding(
                                        padding: const EdgeInsets.all(5.0),
                                        child: Stack(
                                          children: [
                                            Container(
                                              width: 80,
                                              decoration: BoxDecoration(
                                                  color: background,
                                                  borderRadius:
                                                      BorderRadius.circular(15),
                                                  border: Border.all(
                                                    color:
                                                        black.withOpacity(.2),
                                                    width: .5,
                                                  ),
                                                  image: fileType == '.mp4' ||
                                                          fileType == '.m4v' ||
                                                          fileType == '.webm' ||
                                                          fileType == '.flv' ||
                                                          fileType == '.mov' ||
                                                          fileType == '.mpeg' ||
                                                          fileType == '.mkv'
                                                      ? DecorationImage(
                                                          image: MemoryImage(
                                                              selectedMedia[
                                                                      index]
                                                                  .thumbnail!),
                                                          fit: BoxFit.cover)
                                                      : DecorationImage(
                                                          image: FileImage(
                                                              selectedMedia[
                                                                      index]
                                                                  .file!),
                                                          fit: BoxFit.cover)),
                                            ),
                                            Positioned(
                                              right: 5,
                                              top: 5,
                                              child: InkWell(
                                                onTap: () {
                                                  selectedMedia.removeAt(index);
                                                  setState(() {});
                                                },
                                                child: Container(
                                                  decoration:
                                                      const BoxDecoration(
                                                          shape:
                                                              BoxShape.circle,
                                                          color: Colors.red),
                                                  child: const Center(
                                                    child: Padding(
                                                      padding:
                                                          EdgeInsets.all(3),
                                                      child: Icon(
                                                        Icons.close,
                                                        color: white,
                                                        size: 13,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            )
                                          ],
                                        ),
                                      );
                                    }),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: InkWell(
        onTap: () {
          Get.bottomSheet(
            Container(
              height: size.height * .4,
              width: size.width,
              decoration: const BoxDecoration(
                  color: white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  )),
              child: Padding(
                padding: const EdgeInsets.all(15.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: background,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const Center(
                        child: ListTile(
                          leading: Icon(
                            Icons.image,
                            color: Colors.blue,
                            size: 30,
                          ),
                          title: Text('Photo'),
                        ),
                      ),
                    ),
                    sizedBox(10, 0),
                    Container(
                      decoration: BoxDecoration(
                        color: background,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const ListTile(
                        leading: Icon(
                          Icons.video_call_outlined,
                          color: primary,
                          size: 30,
                        ),
                        title: Text('Video'),
                      ),
                    ),
                    sizedBox(10, 0),
                    Container(
                      decoration: BoxDecoration(
                        color: background,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const ListTile(
                        leading: Icon(
                          Icons.emoji_emotions,
                          color: Colors.red,
                          size: 30,
                        ),
                        title: Text('Feelings'),
                      ),
                    ),
                    sizedBox(10, 0),
                    Container(
                      decoration: BoxDecoration(
                        color: background,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: const ListTile(
                        leading: Icon(
                          Icons.location_on_rounded,
                          color: Colors.pink,
                          size: 30,
                        ),
                        title: Text('Location'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
        child: Container(
          width: size.width,
          decoration: const BoxDecoration(
              color: white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              )),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'View post options',
                  style: TextStyle(
                    color: black.withOpacity(0.5),
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.image,
                      color: Colors.blue,
                      size: 30,
                    ),
                    sizedBox(0, 5),
                    const Icon(
                      Icons.emoji_emotions,
                      color: primary,
                      size: 30,
                    ),
                    sizedBox(0, 5),
                    const Icon(
                      Icons.location_on_rounded,
                      color: Colors.pink,
                      size: 30,
                    ),
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
