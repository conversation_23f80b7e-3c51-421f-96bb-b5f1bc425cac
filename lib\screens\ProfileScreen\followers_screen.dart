import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_followers_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';

class UserFollowerScreen extends GetView<GetUserFollowersController> {
  const UserFollowerScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
  final size = MediaQuery.of(context).size;
    Get.lazyPut(() => GetUserFollowersController());
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Followers'),
      body: Obx(() {
        if (controller.isLoading.value) {
          return searchResultShimmer(size);
        } else {
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
            child: GridView.builder(
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    mainAxisSpacing: 10,
                    crossAxisSpacing: 10),
                itemCount: controller.followersData.value.followers!.length,
                itemBuilder: (_, index) {
                  var data = controller.followersData.value.followers![index];
                  return Container(
                    decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                              color: black.withOpacity(0.1), blurRadius: 5)
                        ],
                        color: white,
                        borderRadius: BorderRadius.circular(20),
                        image: DecorationImage(
                            image: NetworkImage(data.avatar!),
                            fit: BoxFit.cover)),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                              color: white,
                              borderRadius: BorderRadius.circular(20)),
                          child: Center(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 5),
                              child: Text(
                                data.name!,
                                textAlign: TextAlign.center,
                                style: const TextStyle(color: black),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
          );
        }
      }),
    );
  }
}
