// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/home/<USER>';
import 'package:flutter_wowonder/screens/GroupsScreen/group_details_screen.dart';
import 'package:flutter_wowonder/screens/PagesScreen/page_details_screen.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/profile_screen.dart';
import 'package:flutter_wowonder/utils/config.dart';
import '../../controllers/posts/post_action_controller.dart';
import '../../utils/colors.dart';
import '../push_navigator.dart';
import '../size_box.dart';

Widget mainPublisher(data, size, context, PostActionController controller) {
  return Row(
    children: [
      Expanded(
        child: Row(
          children: [
            data.isGroupPost == true
                ? Stack(
                    alignment: Alignment.bottomRight,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: data.groupCover ==
                                '$siteUrl/upload/photos/d-cover.jpg  '
                            ? Image.network(
                                '$siteUrl/upload/photos/d-cover.jpg',
                                fit: BoxFit.cover,
                              )
                            : Image.network(
                                data.groupCover!,
                                fit: BoxFit.cover,
                              ),
                      ),
                      Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                        ),
                        child: CircleAvatar(
                          backgroundImage: NetworkImage(data.pubAvatar!),
                        ),
                      ),
                    ],
                  )
                : CircleAvatar(
                    backgroundImage: NetworkImage(data.pubAvatar!),
                  ),
            SizedBox(
              width: size * .6,
              child: ListTile(
                title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (data.isGroupPost == true)
                      Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: (){
                                  pageRoute(context, GroupDetailsScreen(groupID: data.groupID));
                                },
                                child: Text(
                                  data.groupName!,
                                  style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 15,
                                      color: black.withOpacity(.7)),
                                ),
                              ),
                              Row(
                                children: [
                                  Text(
                                    'Posted by ',
                                    style: TextStyle(
                                        color: black.withOpacity(0.4),
                                        fontSize: 11),
                                  ),
                                  Text(
                                    data.pubName!,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: black.withOpacity(.4),
                                      fontSize: 11,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          )
                    else
                      Column(
                            children: [
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      if (data.pageID != null) {
                                        pageRoute(
                                            context,
                                            PageDetailsScreen(
                                                pageID: data.pageID));
                                      } else if (data.pageID == null) {
                                        pageRoute(
                                            context,
                                            MyProfileScreen(
                                                userID: data!.pubId!));
                                      }
                                    },
                                    child: Text(
                                      data.pubName,
                                      style: TextStyle(
                                          fontWeight: FontWeight.w700,
                                          fontSize: 12,
                                          color: black.withOpacity(.7)),
                                    ),
                                  ),
                                  data.isVerified == '1'
                                      ? SvgPicture.asset(
                                          'assets/svg/verify.svg',
                                          width: 17,
                                        )
                                      : Container(),
                                  sizedBox(0, 2),
                                  data.isPro == '1'
                                      ? Image.asset(
                                          'assets/svg/vip.png',
                                          width: 18,
                                        )
                                      : Container(),
                                ],
                              ),
                            ],
                          ),
                  ],
                ),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SvgPicture.asset(
                          'assets/svg/global.svg',
                          width: 15,
                          color: Colors.grey.withOpacity(0.5),
                        ),
                        sizedBox(0, 5),
                        data.postDate == " now"
                            ? Text(
                                'Just Now',
                                style: TextStyle(
                                    color: Colors.grey.withOpacity(0.5),
                                    fontSize: 12),
                              )
                            : Text(
                                data.postDate!,
                                style: TextStyle(
                                    color: Colors.grey.withOpacity(0.5),
                                    fontSize: 12),
                              ),
                      ],
                    ),
                    data.sharedInfo == null
                        ? Container()
                        : Text(
                            'Shared a post',
                            style: TextStyle(color: black.withOpacity(0.3)),
                          )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
      userAction(context, data, size, controller)
    ],
  );
}
