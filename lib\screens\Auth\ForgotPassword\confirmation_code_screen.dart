import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/controllers/auth/forget_password_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../../components/size_box.dart';
import '../../../widgets/button_widget.dart';

class ResetCodeScreen extends GetView<ForgetPasswordController> {
  final String? email;

  const ResetCodeScreen({Key? key, this.email}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(ForgetPasswordController());
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      body: Obx((){
        if(controller.isLoading.value){
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else{
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                sizedBox(10, 0),
                <PERSON>umn(
                  children: [
                    const Text(
                      'Email has been sent!',
                      style: TextStyle(
                          color: black, fontSize: 24, fontWeight: FontWeight.w500),
                    ),
                    sizedBox(10, 0),
                    Text(
                      'Please check your ${email ?? controller.emailController.text}\ninbox and click in the received link to\nreset the password',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: black.withOpacity(0.5),
                      ),
                    ),
                  ],
                ),
                Lottie.asset('assets/json/email_sent.json', width: size * .7),

                Column(
                  children: [
                    CustomButton(
                      onTap: () {
                        navigate('login');
                      },
                      title: 'Sounds good!',
                    ),
                    sizedBox(20, 0),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('Don\'t receive the link?'),
                        sizedBox(0, 5),
                        InkWell(
                          onTap: () {
                            controller.forgetPassword(email: email!, context: context);
                          },
                          child: const Text(
                            'Resend',
                            style: TextStyle(
                                color: primary, fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                sizedBox(0, 0),
              ],
            ),
          );
        }
      }),
    );
  }
}
