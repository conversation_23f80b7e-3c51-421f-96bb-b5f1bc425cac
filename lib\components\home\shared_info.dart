// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../utils/colors.dart';
import '../size_box.dart';

Widget sharedInfo(data, size) {
  return Container(
    decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5),
        border: Border.all(
          color: black.withOpacity(.1),
          width: .5,
        )),
    child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundImage:
                          NetworkImage(data.sharedInfo!.fromAvatar!),
                    ),
                    SizedBox(
                      width: size * .6,
                      child: ListTile(
                        title: Row(
                          children: [
                            Text(
                              data.sharedInfo!.fromName!,
                              style: TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                  color: black.withOpacity(.7)),
                            ),
                            data.sharedInfo!.fromIsVerified == '1'
                                ? SvgPicture.asset(
                                    'assets/svg/verify.svg',
                                    width: 17,
                                  )
                                : Container(),
                            sizedBox(0, 2),
                            data.sharedInfo!.fromIsPro == '1'
                                ? Image.asset(
                                    'assets/svg/vip.png',
                                    width: 18,
                                  )
                                : Container()
                          ],
                        ),
                        subtitle: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SvgPicture.asset(
                              'assets/svg/global.svg',
                              width: 15,
                              color: Colors.grey.withOpacity(0.5),
                            ),
                            sizedBox(0, 5),
                            Text(
                              '${data.sharedInfo!.fromPostTime} ago',
                              style: TextStyle(
                                  color: Colors.grey.withOpacity(0.5),
                                  fontSize: 12),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          const Divider(),
          data.postText == ''
              ? Container()
              : Text(
                  data.sharedInfo!.fromPostText!,
                  textAlign: TextAlign.justify,
                )
        ],
      ),
    ),
  );
}
