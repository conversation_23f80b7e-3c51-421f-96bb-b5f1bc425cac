import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import '../../services/api_services.dart';
import '../../widgets/button_widget.dart';
import '../../widgets/form_helper.dart';
import '../size_box.dart';

class LifeEvent extends StatefulWidget {
  const LifeEvent({Key? key}) : super(key: key);

  @override
  State<LifeEvent> createState() => _LifeEventState();
}

class _LifeEventState extends State<LifeEvent> {
  String relationship = '';
  final school = TextEditingController();
  final working = TextEditingController();
  final website = TextEditingController();
  final profile = Get.put(UserDataController());

  @override
  Widget build(BuildContext context) {
    final userData = profile.getUserDetails.value;
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Life Event'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customFieldHelper(
              controller: school,
              prefixIcon: Icons.school,
              label: 'School',
            ),
            sizedBox(15, 0),
            customFieldHelper(
              controller: working,
              prefixIcon: Icons.work_history_outlined,
              label: 'Working',
            ),
            sizedBox(15, 0),
            customFieldHelper(
              controller: website,
              prefixIcon: Icons.web_outlined,
              label: 'Company Website',
            ),
            sizedBox(15, 0),
            const Text('Relationship'),
            InkWell(
              onTap: () {
                Get.defaultDialog(
                    title: 'Choose Option',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              relationship = '1';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Single'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              relationship = '2';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('In a relationship'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              relationship = '3';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Married'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              relationship = '4';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 8.0),
                              child: Center(
                                child: Text('Engaged'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              },
              child: Container(
                width: size * .9,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(color: black.withOpacity(.1))),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 10.0, vertical: 15),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.favorite,
                        color: black.withOpacity(.5),
                      ),
                      sizedBox(0, 10),
                      Text(
                        relationship == ''
                            ? 'None'
                            : relationship == '1'
                                ? 'Single'
                                : relationship == '2'
                                    ? 'In a relationship'
                                    : relationship == '3'
                                        ? 'Married'
                                        : relationship == '4'
                                            ? 'Engaged'
                                            : 'None',
                        style: TextStyle(
                            color: black.withOpacity(.5), fontSize: 17),
                      )
                    ],
                  ),
                ),
              ),
            ),
            sizedBox(15, 0),
            CustomButton(
              title: 'Save',
              onTap: () {
                APISERvices.updateLifeEvent(
                  context: context,
                  school: school.text.isEmpty ? userData.school : school.text,
                  working:
                      working.text.isEmpty ? userData.working : working.text,
                  website: website.text.isEmpty
                      ? userData.companyWebsite
                      : website.text,
                  relationship: relationship.isEmpty
                      ? userData.relationshipID
                      : relationship,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
