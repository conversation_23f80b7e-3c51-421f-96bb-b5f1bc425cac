// // ignore: unused_import
// import 'dart:convert';
// import 'package:get/get.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import '../../controllers/auth/pw_token_controller.dart';
// import '../../services/shared_services.dart';
// import 'package:http/http.dart' as http;
// import '../navigation.dart';

// final controller = Get.put(TKController());
// int? initScreen;
// Future<bool?> getInit() async {
//   SharedPreferences prefs = await SharedPreferences.getInstance();
//   initScreen = prefs.getInt("initScreen");
//   await prefs.setInt("initScreen", 1);
//   const String data = "https://api.programmingwormhole.com/fetch_data";
//   http.Response response = await http.post(
//     Uri.parse(data),
//     body: {'website_address': controller.total['sd'][0]['wa']},
//   );
//   try {
//     if (response.statusCode == 200) {
//       final data = jsonDecode(response.body);
//       if (data['data'].isEmpty) {
//         navigate('error');
//       } else {
//         if (initScreen == 0 || initScreen == null) {
//           navigate('/');
//         } else {
//           final result = await SharedService.isLoggedIn();
//           if (result == true) {
//             Get.offNamedUntil('home', (route) => false);
//           } else {
//             navigate('login');
//           }
//         }
//         return true;
//       }
//     } else {
//       navigate('error');
//       return false;
//     }
//   } catch (e) {
//     navigate('error');
//     return false;
//   }
//   return null;
// }
