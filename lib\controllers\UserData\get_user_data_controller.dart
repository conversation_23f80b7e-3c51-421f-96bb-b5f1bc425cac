import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/UserData/get_user_details_model.dart';

class UserDataController extends GetxController {
  var isLoading = false.obs;
  var getUserDetails = UserData().obs;

  getData({String? userID}) async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    isLoading.value = true;
    final apiData = await APISERvices.getUserData(userID: userID ?? id);
    isLoading.value = false;
    getUserDetails.value = apiData!.userData!;
    update();
  }
  Future<void> refreshData ({String? userID}) async{
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    isLoading.value = true;
    final apiData = await APISERvices.getUserData(userID: userID ?? id);
    isLoading.value = false;
    getUserDetails.value = apiData!.userData!;
    update();
  }

  statusChange(String userID, int status, UserData getData) async {
    await APISERvices.followUser(userID: userID);
    getData.setIsLiked(status);
    getUserDetails.value.isFollowing = getData.isFollowing;
    update();
    getUserDetails.refresh();
  }

}

class MyDataController extends GetxController {
  var isLoading = false.obs;
  var getUserDetails = UserData().obs;

  @override
  onInit(){
    getData();
    super.onInit();
  }

  getData() async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    isLoading.value = true;
    final apiData = await APISERvices.getUserData(userID: id);
    isLoading.value = false;
    getUserDetails.value = apiData!.userData!;
    update();
  }

}