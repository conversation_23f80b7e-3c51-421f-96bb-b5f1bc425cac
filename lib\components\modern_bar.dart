import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/bottom_bar_controller.dart';
import '../utils/colors.dart';
import 'modern_bar_item.dart';

class ModernBar extends StatelessWidget {
  const ModernBar({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    const Color selectedColor = primary;
    final Color unSelectedColor = black.withOpacity(0.5);
    final BottomBarController controller =
        Get.put(BottomBarController(), permanent: false);
    final size = MediaQuery.of(context).size.width;
    return Obx(() => Container(
          width: size,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: black.withOpacity(.1),
                blurRadius: 15,
              )
            ],
            color: white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: BottomNavigationBar(
            type: BottomNavigationBarType.shifting,
            showUnselectedLabels: false,
            showSelectedLabels: true,
            onTap: controller.changeTabIndex,
            currentIndex: controller.tabIndex.value,
            backgroundColor: Colors.transparent,
            elevation: 0,
            unselectedItemColor: unSelectedColor,
            selectedItemColor: selectedColor,
            items: [
              modernBarItems(
                  controller,
                  'assets/svg/home_fill.svg',
                  'assets/svg/home_outline.svg',
                  'Home',
                  selectedColor,
                  unSelectedColor,
                  0),
              modernBarItems(
                  controller,
                  'assets/svg/play_fill.svg',
                  'assets/svg/play_outline.svg',
                  'Videos',
                  selectedColor,
                  unSelectedColor,
                  1),
              modernBarItems(
                  controller,
                  'assets/svg/user_filled.svg',
                  'assets/svg/user_outline.svg',
                  'Find Friends',
                  selectedColor,
                  unSelectedColor,
                  2),
              modernBarItems(
                  controller,
                  'assets/svg/notification_fill.svg',
                  'assets/svg/notification_outline.svg',
                  'Notification',
                  selectedColor,
                  unSelectedColor,
                  3),
              pngModernBarItems(
                  controller,
                  'assets/svg/more_fill.svg',
                  'assets/svg/more_outline.svg',
                  'More',
                  selectedColor,
                  unSelectedColor,
                  4)
            ],
          ),
        ));
  }
}
