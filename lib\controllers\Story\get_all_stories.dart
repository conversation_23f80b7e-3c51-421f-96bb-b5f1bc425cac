import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

class GetAllStoriesController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var getAllStories = {}.obs;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  getData({String? tokenID}) async {
    isLoading.value = true;
    var apiData = await apiServices.getAllStories(tokenID: tokenID);
    isLoading.value = false;
    getAllStories.value = apiData!;
    update();
  }
}

