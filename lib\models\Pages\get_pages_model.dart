// To parse this JSON data, do
//
//     final getPagesModel = getPagesModelFromJson(jsonString);

import 'dart:convert';

GetPagesModel getPagesModelFromJson(String str) => GetPagesModel.fromJson(json.decode(str));

String getPagesModelToJson(GetPagesModel data) => json.encode(data.toJson());

class GetPagesModel {
  int? apiStatus;
  List<Datum>? data;

  GetPagesModel({
    this.apiStatus,
    this.data,
  });

  factory GetPagesModel.fromJson(Map<String, dynamic> json) => GetPagesModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class Datum {
  String? pageId;
  String? userId;
  String? pageName;
  String? pageTitle;
  String? pageDescription;
  String? avatar;
  String? cover;
  String? usersPost;
  String? pageCategory;
  String? subCategory;
  String? website;
  String? facebook;
  String? google;
  String? vk;
  String? twitter;
  String? linkedin;
  String? company;
  String? phone;
  String? address;
  String? callActionType;
  String? callActionTypeUrl;
  String? backgroundImage;
  String? backgroundImageStatus;
  String? instgram;
  String? youtube;
  String? verified;
  String? active;
  String? registered;
  String? boosted;
  String? time;
  String? about;
  String? id;
  String? type;
  String? url;
  String? name;
  int? rating;
  String? category;
  String? pageSubCategory;
  bool? isReported;
  bool? isPageOnwer;
  String? username;
  List<dynamic>? fields;
  String? likes;
  bool? isLiked;

  Datum({
    this.pageId,
    this.userId,
    this.pageName,
    this.pageTitle,
    this.pageDescription,
    this.avatar,
    this.cover,
    this.usersPost,
    this.pageCategory,
    this.subCategory,
    this.website,
    this.facebook,
    this.google,
    this.vk,
    this.twitter,
    this.linkedin,
    this.company,
    this.phone,
    this.address,
    this.callActionType,
    this.callActionTypeUrl,
    this.backgroundImage,
    this.backgroundImageStatus,
    this.instgram,
    this.youtube,
    this.verified,
    this.active,
    this.registered,
    this.boosted,
    this.time,
    this.about,
    this.id,
    this.type,
    this.url,
    this.name,
    this.rating,
    this.category,
    this.pageSubCategory,
    this.isReported,
    this.isPageOnwer,
    this.username,
    this.fields,
    this.likes,
    this.isLiked,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
    pageId: json["page_id"],
    userId: json["user_id"],
    pageName: json["page_name"],
    pageTitle: json["page_title"],
    pageDescription: json["page_description"],
    avatar: json["avatar"],
    cover: json["cover"],
    usersPost: json["users_post"],
    pageCategory: json["page_category"],
    subCategory: json["sub_category"],
    website: json["website"],
    facebook: json["facebook"],
    google: json["google"],
    vk: json["vk"],
    twitter: json["twitter"],
    linkedin: json["linkedin"],
    company: json["company"],
    phone: json["phone"],
    address: json["address"],
    callActionType: json["call_action_type"],
    callActionTypeUrl: json["call_action_type_url"],
    backgroundImage: json["background_image"],
    backgroundImageStatus: json["background_image_status"],
    instgram: json["instgram"],
    youtube: json["youtube"],
    verified: json["verified"],
    active: json["active"],
    registered: json["registered"],
    boosted: json["boosted"],
    time: json["time"],
    about: json["about"],
    id: json["id"],
    type: json["type"],
    url: json["url"],
    name: json["name"],
    rating: json["rating"],
    category: json["category"],
    pageSubCategory: json["page_sub_category"],
    isReported: json["is_reported"],
    isPageOnwer: json["is_page_onwer"],
    username: json["username"],
    fields: json["fields"] == null ? [] : List<dynamic>.from(json["fields"]!.map((x) => x)),
    likes: json["likes"],
    isLiked: json['is_liked'] ?? false
  );

  Map<String, dynamic> toJson() => {
    "page_id": pageId,
    "user_id": userId,
    "page_name": pageName,
    "page_title": pageTitle,
    "page_description": pageDescription,
    "avatar": avatar,
    "cover": cover,
    "users_post": usersPost,
    "page_category": pageCategory,
    "sub_category": subCategory,
    "website": website,
    "facebook": facebook,
    "google": google,
    "vk": vk,
    "twitter": twitter,
    "linkedin": linkedin,
    "company": company,
    "phone": phone,
    "address": address,
    "call_action_type": callActionType,
    "call_action_type_url": callActionTypeUrl,
    "background_image": backgroundImage,
    "background_image_status": backgroundImageStatus,
    "instgram": instgram,
    "youtube": youtube,
    "verified": verified,
    "active": active,
    "registered": registered,
    "boosted": boosted,
    "time": time,
    "about": about,
    "id": id,
    "type": type,
    "url": url,
    "name": name,
    "rating": rating,
    "category": category,
    "page_sub_category": pageSubCategory,
    "is_reported": isReported,
    "is_page_onwer": isPageOnwer,
    "username": username,
    "fields": fields == null ? [] : List<dynamic>.from(fields!.map((x) => x)),
    "likes": likes,
  };
}
