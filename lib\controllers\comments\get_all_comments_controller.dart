import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

import '../../../models/Comments/post_comment_model.dart';

class GetAllPostCommentController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  final commentData = PostCommentModel().data.obs;

  getData({required String type, required String postID}) async {
    isLoading.value = true;
    var apiData = await apiServices.getAllComment(type: type, postID: postID);
    commentData.value = apiData!.data!;
    isLoading.value = false;
    update();
  }

  statusChange(String id, String status, CommentData getCountries, int index,
      {String? ccc}) async {
    await apiServices.commentReaction(commentID: id, action: status, reaction: ccc);
    getCountries.setIsActive(ccc.toString());
    commentData.value![index] = getCountries;
    commentData.refresh();
    update();
  }
}
