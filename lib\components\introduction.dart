import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';

class IntroScreen1 extends StatelessWidget {
  final String image;
  final String title;
  final String dec;
  const IntroScreen1({Key? key, required this.image, required this.title, required this.dec}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: background,
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(image),
            Text(title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: black.withOpacity(0.5),
              fontSize: 35,
              fontWeight: FontWeight.w500
            ),),
            sizedBox(10, 0),
            Text(dec,
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontWeight: FontWeight.w300,
            ),),
          ],
        ),
      ),
    );
  }
}
