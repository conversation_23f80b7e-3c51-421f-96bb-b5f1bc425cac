// ignore_for_file: deprecated_member_use

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/profile_screen.dart';
import '../../controllers/UserData/get_user_data_controller.dart';
import '../../controllers/posts/get_all_post_controller.dart';
import '../../controllers/posts/get_vide_post_controller.dart';
import '../../models/PostModels/get_all_post_model.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';
import '../../widgets/button_widget.dart';
import '../CommentScreen/comments_screen.dart';
import 'content_screen.dart';
import 'package:get/get.dart';

class ReelScreen extends StatefulWidget {
  final double size;

  const ReelScreen({Key? key, required this.size}) : super(key: key);

  @override
  State<ReelScreen> createState() => _ReelScreenState();
}

class _ReelScreenState extends State<ReelScreen> {
  final controller = Get.put(GetVideoPostController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: black,
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: PageView.builder(
            itemCount: controller.dataList.length,
            scrollDirection: Axis.vertical,
            itemBuilder: (_, index) {
              var data = controller.dataList[index];
              return Stack(
                fit: StackFit.expand,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: ContentScreen(
                      src: data.postFile,
                    ),
                  ),
                  OptionBuilder(
                    data: data,
                    index: index,
                  )
                ],
              );
            }),
      ),
    );
  }
}

class OptionBuilder extends StatefulWidget {
  final Datum data;
  final int index;

  const OptionBuilder({Key? key, required this.data, required this.index})
      : super(key: key);

  @override
  State<OptionBuilder> createState() => _OptionBuilderState();
}

class _OptionBuilderState extends State<OptionBuilder> {
  final userData = Get.put(MyDataController());
  final postController = Get.put(GetAllPostController());
  final shareTextController = TextEditingController();
  final controller = Get.put(GetVideoPostController());

  @override
  Widget build(BuildContext context) {
    bool? postLiked = widget.data.reactType == '1' ? true : false;
    final size = MediaQuery.of(context).size;
    return Obx(() {
      if (controller.isLoading.value) {
        return const CircularProgressIndicator();
      } else {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Column(
                    children: [
                      InkWell(
                        onTap: () {
                          if (widget.data.reactType == "1") {
                            setState(() {
                              postLiked = false;
                            });
                            controller.statusChange(
                              widget.data.postId!,
                              "reaction",
                              widget.data,
                              widget.index,
                              ccc: "",
                            );
                          } else {
                            setState(() {
                              postLiked = true;
                            });
                            AudioPlayer().play(AssetSource('audios/liked.mp3'));
                            controller.statusChange(
                              widget.data.postId!,
                              "reaction",
                              widget.data,
                              widget.index,
                              ccc: "1",
                            );
                          }
                        },
                        child: SvgPicture.asset(
                          'assets/svg/heart.svg',
                          width: 35,
                          colorFilter: ColorFilter.mode(
                            postLiked == true ? primary : white,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                      sizedBox(20, 0),
                      widget.data.isComment == '0'
                          ? SvgPicture.asset(
                              'assets/svg/comment.svg',
                              color: white.withOpacity(.5),
                              width: 30,
                            )
                          : InkWell(
                              onTap: () {
                                pageRoute(
                                    context,
                                    PostCommentScreen(
                                      postID: widget.data.postId!,
                                    ));
                              },
                              child: SvgPicture.asset(
                                'assets/svg/comment.svg',
                                color: white,
                                width: 30,
                              ),
                            ),
                      sizedBox(5, 0),
                      widget.data.isComment == '0'
                          ? Text('None',
                              style: TextStyle(
                                color: white.withOpacity(.5),
                              ))
                          : Text(
                              widget.data.totalComments!,
                              style: const TextStyle(
                                color: white,
                              ),
                            ),
                      sizedBox(20, 0),
                      InkWell(
                        onTap: () {
                          Get.bottomSheet(
                            BottomSheet(
                              backgroundColor: Colors.transparent,
                              onClosing: () {},
                              builder: (_) => Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: Container(
                                  height: size.height * .360,
                                  width: size.width * .9,
                                  decoration: BoxDecoration(
                                    color: white,
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Center(
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 15.0, vertical: 15),
                                      child: SingleChildScrollView(
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Text(
                                                  'Share to timeline',
                                                  style: TextStyle(
                                                      color:
                                                          black.withOpacity(.5),
                                                      fontSize: 20,
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                Container(
                                                  decoration: BoxDecoration(
                                                      color: Colors.grey
                                                          .withOpacity(.1),
                                                      shape: BoxShape.circle),
                                                  child: InkWell(
                                                    onTap: () {
                                                      Navigator.pop(context);
                                                      Navigator.pop(context);
                                                    },
                                                    child: const Padding(
                                                      padding:
                                                          EdgeInsets.all(3.0),
                                                      child: Center(
                                                        child: Icon(
                                                          Icons.close,
                                                          color: black,
                                                          size: 20,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                )
                                              ],
                                            ),
                                            sizedBox(10, 0),
                                            Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                CircleAvatar(
                                                  backgroundImage: NetworkImage(
                                                      userData.getUserDetails
                                                          .value.avatar!),
                                                ),
                                                sizedBox(0, 5),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  children: [
                                                    Text(
                                                      userData.getUserDetails
                                                          .value.name!,
                                                      style: const TextStyle(
                                                          color: black,
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w400),
                                                    ),
                                                    sizedBox(1, 0),
                                                    Text(
                                                      'Share post publicly',
                                                      style: TextStyle(
                                                        color: black
                                                            .withOpacity(.4),
                                                      ),
                                                    )
                                                  ],
                                                )
                                              ],
                                            ),
                                            sizedBox(10, 0),
                                            Container(
                                              height: size.height * .100,
                                              width: size.width * .9,
                                              decoration: BoxDecoration(
                                                  borderRadius:
                                                      BorderRadius.circular(20),
                                                  color: background),
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: 10.0),
                                                child: Center(
                                                  child: TextFormField(
                                                    controller:
                                                        shareTextController,
                                                    textAlign:
                                                        TextAlign.justify,
                                                    maxLines: 10,
                                                    minLines: 10,
                                                    decoration:
                                                        const InputDecoration(
                                                      hintText:
                                                          'What\'s about for this post?',
                                                      border: InputBorder.none,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ),
                                            sizedBox(10, 0),
                                            CustomButton(
                                              title: 'Share',
                                              onTap: () {
                                                APISERvices.postShare(
                                                  postID: widget.data.postId!,
                                                  shareText:
                                                      shareTextController.text,
                                                );
                                                Get.snackbar(
                                                  'Shared!',
                                                  'Post Shared successfully to your profile.',
                                                  snackPosition:
                                                      SnackPosition.BOTTOM,
                                                );
                                                Navigator.pop(context);
                                              },
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                        child: SvgPicture.asset(
                          'assets/svg/share.svg',
                          color: white,
                          width: 30,
                        ),
                      ),
                      sizedBox(5, 0),
                      Text(
                        widget.data.totalShares!,
                        style: const TextStyle(
                          color: white,
                        ),
                      )
                    ],
                  ),
                ],
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      pageRoute(
                          context, MyProfileScreen(userID: widget.data.pubId!));
                    },
                    child: Text(
                      widget.data.pubName!,
                      style: const TextStyle(
                          color: white,
                          fontSize: 16,
                          fontWeight: FontWeight.w600),
                    ),
                  ),
                  widget.data.isVerified == '1'
                      ? SvgPicture.asset(
                          'assets/svg/verify.svg',
                          width: 15,
                        )
                      : Container(),
                  sizedBox(0, 2),
                  widget.data.isPro == '1'
                      ? Image.asset(
                          'assets/svg/vip.png',
                          width: 15,
                        )
                      : Container(),
                ],
              ),
              sizedBox(5, 0),
              Text(
                widget.data.postText!,
                style: TextStyle(
                  color: white.withOpacity(.7),
                ),
                maxLines: 2,
              )
            ],
          ),
        );
      }
    });
  }
}
