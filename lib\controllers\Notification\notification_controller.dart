import 'package:flutter_wowonder/models/Notification/notification_model.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

class NotificationController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  final notificationData = NotificationsModel().obs;

  @override
  onInit(){
    getData();
    super.onInit();
  }

  getData() async {
    isLoading.value = true;
    var apiData = await apiServices.getAllNotification();
    notificationData.value = apiData!;
    isLoading.value = false;
    update();
  }
  void onRefresh() async {
    isLoading.value = true;
    var apiData = await apiServices.getAllNotification();
    notificationData.value = apiData!;
    isLoading.value = false;
  }
}
