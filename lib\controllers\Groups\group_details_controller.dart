import 'package:get/get.dart';
import '../../models/Groups/group_details_model.dart';
import '../../services/api_services.dart';

class GroupDetailsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var groupData = GroupData().obs;

  getData({required String pageID}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getGroupData(groupID: pageID);
    groupData.value = apiData!.groupData!;
    isLoading.value = false;
    update();
  }

  statusChange(String groupID, int status , GroupData getData) async {
    await APISERvices.joinGroup(groupID: groupID);
    getData.setIsJoined(status);
    groupData.value.isGroupJoined = getData.isGroupJoined;
    update();
    groupData.refresh();
  }
}