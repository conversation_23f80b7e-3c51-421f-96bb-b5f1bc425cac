// To parse this JSON data, do
//
//     final getGroupsModel = getGroupsModelFromJson(jsonString);

import 'dart:convert';

GetGroupsModel getGroupsModelFromJson(String str) => GetGroupsModel.fromJson(json.decode(str));

String getGroupsModelToJson(GetGroupsModel data) => json.encode(data.toJson());

class GetGroupsModel {
  int? apiStatus;
  List<GroupsDatum>? data;

  GetGroupsModel({
    this.apiStatus,
    this.data,
  });

  factory GetGroupsModel.fromJson(Map<String, dynamic> json) => GetGroupsModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<GroupsDatum>.from(json["data"]!.map((x) => GroupsDatum.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class GroupsDatum {
  String? id;
  String? userId;
  String? groupName;
  String? groupTitle;
  String? avatar;
  String? cover;
  String? about;
  String? category;
  String? subCategory;
  String? privacy;
  String? joinPrivacy;
  String? active;
  String? registered;
  String? time;
  String? groupId;
  String? url;
  String? name;
  String? categoryId;
  String? type;
  String? username;
  bool? isReported;
  String? groupSubCategory;
  List<dynamic>? fields;
  int? isGroupJoined;
  String? membersCount;
  String? members;

  GroupsDatum({
    this.id,
    this.userId,
    this.groupName,
    this.groupTitle,
    this.avatar,
    this.cover,
    this.about,
    this.category,
    this.subCategory,
    this.privacy,
    this.joinPrivacy,
    this.active,
    this.registered,
    this.time,
    this.groupId,
    this.url,
    this.name,
    this.categoryId,
    this.type,
    this.username,
    this.isReported,
    this.groupSubCategory,
    this.fields,
    this.isGroupJoined,
    this.membersCount,
    this.members,
  });

  factory GroupsDatum.fromJson(Map<String, dynamic> json) => GroupsDatum(
    id: json["id"],
    userId: json["user_id"],
    groupName: json["group_name"],
    groupTitle: json["group_title"],
    avatar: json["avatar"],
    cover: json["cover"],
    about: json["about"],
    category: json["category"],
    subCategory: json["sub_category"],
    privacy: json["privacy"],
    joinPrivacy: json["join_privacy"],
    active: json["active"],
    registered: json["registered"],
    time: json["time"],
    groupId: json["group_id"],
    url: json["url"],
    name: json["name"],
    categoryId: json["category_id"],
    type: json["type"],
    username: json["username"],
    isReported: json["is_reported"],
    groupSubCategory: json["group_sub_category"],
    fields: json["fields"] == null ? [] : List<dynamic>.from(json["fields"]!.map((x) => x)),
    isGroupJoined: json["is_group_joined"],
    membersCount: json["members_count"],
    members: json["members"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user_id": userId,
    "group_name": groupName,
    "group_title": groupTitle,
    "avatar": avatar,
    "cover": cover,
    "about": about,
    "category": category,
    "sub_category": subCategory,
    "privacy": privacy,
    "join_privacy": joinPrivacy,
    "active": active,
    "registered": registered,
    "time": time,
    "group_id": groupId,
    "url": url,
    "name": name,
    "category_id": categoryId,
    "type": type,
    "username": username,
    "is_reported": isReported,
    "group_sub_category": groupSubCategory,
    "fields": fields == null ? [] : List<dynamic>.from(fields!.map((x) => x)),
    "is_group_joined": isGroupJoined,
    "members_count": membersCount,
    "members": members,
  };
}
