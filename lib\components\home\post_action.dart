// ignore_for_file: deprecated_member_use

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/controllers/posts/get_all_post_controller.dart';
import 'package:get/get.dart';
import '../../controllers/UserData/get_user_data_controller.dart';
import '../../screens/CommentScreen/comments_screen.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';
import '../../widgets/button_widget.dart';
import '../size_box.dart';

Widget postAction(controller, data, int index, BuildContext context) {
  final size = MediaQuery.of(context).size;
  return Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
      Row(
        children: [
          InkWell(
            onTap: () {
              likeAction(data, controller, index);
            },
            child: SvgPicture.asset(
              likeIcon,
              width: 26,
              colorFilter: ColorFilter.mode(
                data.reactType == "1" ? primary : black.withOpacity(0.5),
                BlendMode.srcIn,
              ),
            ),
          ),
          sizedBox(0, 20),
          InkWell(
            onTap: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (_) => PostCommentScreen(postID: data.postId!)));
            },
            child: SvgPicture.asset(
              'assets/svg/comment.svg',
              width: 26,
              color: black.withOpacity(0.5),
            ),
          ),
          sizedBox(0, 20),
          InkWell(
            onTap: () {
              sharePost(size, context, data);
            },
            child: SvgPicture.asset(
              'assets/svg/share.svg',
              width: 26,
              color: black.withOpacity(0.5),
            ),
          ),
        ],
      ),
      Text(
        '${data.totalLikes} reactions',
        style: TextStyle(
          fontSize: 13,
          color: Colors.grey.withOpacity(0.8),
        ),
      )
    ],
  );
}

void likeAction(data, controller, index) {
  if (data.reactType == "1") {
    controller.statusChange(data.postId!, "reaction", data, index, ccc: "");
  } else {
    AudioPlayer().play(AssetSource('audios/liked.mp3'));
    controller.statusChange(data.postId!, "reaction", data, index, ccc: "1");
  }
}

void sharePost(Size size, BuildContext context, data) {
  Get.bottomSheet(BottomSheet(
      backgroundColor: Colors.transparent,
      onClosing: () {},
      builder: (_) => Padding(
            padding: const EdgeInsets.all(10.0),
            child: Container(
              height: size.height * .350,
              width: size.width * .9,
              decoration: BoxDecoration(
                color: white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 15.0, vertical: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Share',
                            style: TextStyle(
                                color: black.withOpacity(.5),
                                fontSize: 20,
                                fontWeight: FontWeight.bold),
                          ),
                          Container(
                            decoration: BoxDecoration(
                                color: Colors.grey.withOpacity(.1),
                                shape: BoxShape.circle),
                            child: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: const Padding(
                                padding: EdgeInsets.all(3.0),
                                child: Center(
                                  child: Icon(
                                    Icons.close,
                                    color: black,
                                    size: 20,
                                  ),
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          InkWell(
                            onTap: () {
                              createPost(size, context, data);
                            },
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  decoration: BoxDecoration(
                                      color: Colors.grey.withOpacity(.1),
                                      shape: BoxShape.circle),
                                  child: Padding(
                                    padding: const EdgeInsets.all(10.0),
                                    child: Icon(
                                      Icons.edit,
                                      color: black.withOpacity(.5),
                                      size: 30,
                                    ),
                                  ),
                                ),
                                sizedBox(0, 5),
                                Text(
                                  'Create Post',
                                  style: TextStyle(
                                      color: black.withOpacity(.5),
                                      fontSize: 13),
                                )
                              ],
                            ),
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(.1),
                                    shape: BoxShape.circle),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Icon(
                                    Icons.facebook,
                                    color: black.withOpacity(.5),
                                    size: 30,
                                  ),
                                ),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Facebook',
                                style: TextStyle(
                                    color: black.withOpacity(.5), fontSize: 13),
                              )
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(.1),
                                    shape: BoxShape.circle),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Icon(
                                    Icons.message,
                                    color: black.withOpacity(.5),
                                    size: 30,
                                  ),
                                ),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'Message',
                                style: TextStyle(
                                    color: black.withOpacity(.5), fontSize: 13),
                              )
                            ],
                          ),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(.1),
                                    shape: BoxShape.circle),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Icon(
                                    Icons.more_vert_sharp,
                                    color: black.withOpacity(.5),
                                    size: 30,
                                  ),
                                ),
                              ),
                              sizedBox(0, 5),
                              Text(
                                'More...',
                                style: TextStyle(
                                    color: black.withOpacity(.5), fontSize: 13),
                              )
                            ],
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Post Link',
                            style: TextStyle(
                                color: black.withOpacity(.5),
                                fontSize: 18,
                                fontWeight: FontWeight.w400),
                          ),
                          sizedBox(10, 0),
                          Container(
                            width: size.width * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8.0, vertical: 8),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: Text(
                                      data.postLink!,
                                      style: TextStyle(
                                          color: black.withOpacity(.5)),
                                    ),
                                  ),
                                  InkWell(
                                      onTap: () {
                                        Clipboard.setData(ClipboardData(
                                            text: data.postLink!));
                                        Get.snackbar(
                                          'Copied!',
                                          'Post link has been successfully copied to clipboard.',
                                          snackPosition: SnackPosition.BOTTOM,
                                        );
                                        Navigator.pop(context);
                                      },
                                      child: Icon(
                                        Icons.copy_sharp,
                                        color: black.withOpacity(.5),
                                        size: 20,
                                      ))
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          )));
}

Future createPost(Size size, BuildContext context, data) {
  final userData = Get.put(MyDataController());
  final postController = Get.put(GetAllPostController());
  final shareTextController = TextEditingController();
  return Get.bottomSheet(
    BottomSheet(
      backgroundColor: Colors.transparent,
      onClosing: () {},
      builder: (_) => Padding(
        padding: const EdgeInsets.all(10.0),
        child: Container(
          height: size.height * .360,
          width: size.width * .9,
          decoration: BoxDecoration(
            color: white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15),
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Share to timeline',
                          style: TextStyle(
                              color: black.withOpacity(.5),
                              fontSize: 20,
                              fontWeight: FontWeight.bold),
                        ),
                        Container(
                          decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              shape: BoxShape.circle),
                          child: InkWell(
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.pop(context);
                            },
                            child: const Padding(
                              padding: EdgeInsets.all(3.0),
                              child: Center(
                                child: Icon(
                                  Icons.close,
                                  color: black,
                                  size: 20,
                                ),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                    sizedBox(10, 0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CircleAvatar(
                          backgroundImage: NetworkImage(
                              userData.getUserDetails.value.avatar!),
                        ),
                        sizedBox(0, 5),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              userData.getUserDetails.value.name!,
                              style: const TextStyle(
                                  color: black,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w400),
                            ),
                            sizedBox(1, 0),
                            Text(
                              'Share post publicly',
                              style: TextStyle(
                                color: black.withOpacity(.4),
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                    sizedBox(10, 0),
                    Container(
                      height: size.height * .100,
                      width: size.width * .9,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          color: background),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10.0),
                        child: Center(
                          child: TextFormField(
                            controller: shareTextController,
                            textAlign: TextAlign.justify,
                            maxLines: 10,
                            minLines: 10,
                            decoration: const InputDecoration(
                              hintText: 'What\'s about for this post?',
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                      ),
                    ),
                    sizedBox(10, 0),
                    CustomButton(
                      title: 'Share',
                      onTap: () {
                        APISERvices.postShare(
                          postID: data.postId!,
                          shareText: shareTextController.text,
                        );
                        Get.snackbar(
                          'Shared!',
                          'Post Shared successfully to your profile.',
                          snackPosition: SnackPosition.BOTTOM,
                        );
                        postController.getData();
                        Navigator.pop(context);
                        Navigator.pop(context);
                      },
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
