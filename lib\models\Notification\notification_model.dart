// To parse this JSON data, do
//
//     final notificationsModel = notificationsModelFromJson(jsonString);

import 'dart:convert';

NotificationsModel notificationsModelFromJson(String str) => NotificationsModel.fromJson(json.decode(str));


class NotificationsModel {
  int? apiStatus;
  List<Notification>? notifications;
  String? newNotificationsCount;

  NotificationsModel({
    this.apiStatus,
    this.notifications,
    this.newNotificationsCount,
  });

  factory NotificationsModel.fromJson(Map<String, dynamic> json) => NotificationsModel(
    apiStatus: json["api_status"],
    notifications: json["notifications"] == null ? [] : List<Notification>.from(json["notifications"]!.map((x) => Notification.fromJson(x))),
    newNotificationsCount: json["new_notifications_count"],
  );

}

class Notification {
  String? id;
  String? notifierId;
  String? recipientId;
  String? postId;
  String? replyId;
  String? commentId;
  String? pageId;
  String? groupId;
  String? groupChatId;
  String? eventId;
  String? threadId;
  String? blogId;
  String? storyId;
  String? seenPop;
  String? type;
  String? type2;
  String? text;
  String? url;
  String? fullLink;
  String? seen;
  String? sentPush;
  String? admin;
  String? time;
  String? typeText;
  String? timeTextString;
  String? notifierName;
  String? notifierAvatar;
  String? notifiedVerified;

  Notification({
    this.id,
    this.notifierId,
    this.recipientId,
    this.postId,
    this.replyId,
    this.commentId,
    this.pageId,
    this.groupId,
    this.groupChatId,
    this.eventId,
    this.threadId,
    this.blogId,
    this.storyId,
    this.seenPop,
    this.type,
    this.type2,
    this.text,
    this.url,
    this.fullLink,
    this.seen,
    this.sentPush,
    this.admin,
    this.time,
    this.typeText,
    this.timeTextString,
    this.notifierAvatar,
    this.notifierName,
    this.notifiedVerified,
  });

  factory Notification.fromJson(Map<String, dynamic> json) => Notification(
    id: json["id"],
    notifierId: json["notifier_id"],
    recipientId: json["recipient_id"],
    postId: json["post_id"],
    replyId: json["reply_id"],
    commentId: json["comment_id"],
    pageId: json["page_id"],
    groupId: json["group_id"],
    groupChatId: json["group_chat_id"],
    eventId: json["event_id"],
    threadId: json["thread_id"],
    blogId: json["blog_id"],
    storyId: json["story_id"],
    seenPop: json["seen_pop"],
    type: json["type"],
    type2: json["type2"],
    text: json["text"],
    url: json["url"],
    notifierAvatar: json['notifier']['avatar'],
    notifierName: json['notifier']['name'],
    notifiedVerified: json['notifier']['verified'],
    fullLink: json["full_link"],
    seen: json["seen"],
    sentPush: json["sent_push"],
    admin: json["admin"],
    time: json["time"],
    typeText: json["type_text"],
    timeTextString: json["time_text_string"],
  );

}
