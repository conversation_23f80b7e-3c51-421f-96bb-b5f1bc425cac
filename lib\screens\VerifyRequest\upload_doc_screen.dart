import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/VerifyRequest/send_request_screen.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:lottie/lottie.dart';
import '../../utils/colors.dart';

class UploadDocScreen extends StatefulWidget {
  final String name;
  final String email;
  final String phoneNumber;
  final String dateOfBirth;

  const UploadDocScreen({
    Key? key,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.dateOfBirth,
  }) : super(key: key);

  @override
  State<UploadDocScreen> createState() => _UploadDocScreenState();
}

class _UploadDocScreenState extends State<UploadDocScreen> {
  File? _image;
  File? _selfie;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    void pickImage() async {
      try {
        final image =
            await ImagePicker().pickImage(source: ImageSource.gallery);
        if (image == null) return;
        File? img = File(image.path);
        setState(() {
          _image = img;
        });
      } on PlatformException {
        return null;
      }
    }

    void takeSelfie() async {
      try {
        final image = await ImagePicker().pickImage(
          source: ImageSource.camera,
          preferredCameraDevice: CameraDevice.front,
        );
        if (image == null) return;
        File? img = File(image.path);
        // img = await cropImageFile(imageFile: img);
        setState(() {
          _selfie = img;
        });
      } on PlatformException {
        return null;
      }
    }


    return Scaffold(
      backgroundColor: background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: background,
        title: Text(
          'Upload Documents',
          style: TextStyle(
              color: black.withOpacity(.5),
              fontSize: 17,
              fontWeight: FontWeight.w400),
        ),
        centerTitle: true,
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              Icons.arrow_back_ios_new,
              color: black.withOpacity(.5),
            )),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            sizedBox(10, 0),
            Center(
              child: Container(
                width: size.width * .9,
                decoration: BoxDecoration(
                    color: white.withOpacity(.5),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: black.withOpacity(.2),
                    )),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Lottie.asset(
                        'assets/json/id_card.json',
                        width: size.width * .3,
                      ),
                      const Text(
                        'Identity Card',
                        style: TextStyle(
                            color: black,
                            fontWeight: FontWeight.w500,
                            fontSize: 25),
                      ),
                      Text(
                        'Take your identity card to check\nyour information.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: black.withOpacity(.3),
                        ),
                      ),
                      sizedBox(10, 0),
                      InkWell(
                        onTap: () {
                          pickImage();
                        },
                        child: Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Container(
                                decoration: BoxDecoration(
                                    color: primary,
                                    borderRadius: BorderRadius.circular(8)),
                                child: const Padding(
                                  padding: EdgeInsets.all(5.0),
                                  child: Icon(
                                    Icons.link_outlined,
                                    color: white,
                                  ),
                                ),
                              ),
                              title: const Text(
                                'Identity Scan',
                              ),
                              subtitle: Text(
                                _image == null ? 'Select Photo' : 'Uploaded',
                                style: TextStyle(color: black.withOpacity(.2)),
                              ),
                              trailing: Container(
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: black.withOpacity(.1)),
                                  color: white.withOpacity(.1),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: Icon(
                                    Icons.edit,
                                    color: black.withOpacity(.3),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            sizedBox(10, 0),
            Center(
              child: Container(
                width: size.width * .9,
                decoration: BoxDecoration(
                    color: white.withOpacity(.5),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: black.withOpacity(.2),
                    )),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15.0,
                    vertical: 10,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Lottie.asset(
                        'assets/json/selfie.json',
                        width: size.width * .3,
                      ),
                      const Text(
                        'Selfie Photo',
                        style: TextStyle(
                            color: black,
                            fontWeight: FontWeight.w500,
                            fontSize: 25),
                      ),
                      Text(
                        'Having your selfie taken to allow us to\nconfirm your identity.',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: black.withOpacity(.3),
                        ),
                      ),
                      sizedBox(10, 0),
                      InkWell(
                        onTap: () {
                          takeSelfie();
                        },
                        child: Container(
                          width: size.width * .9,
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(.1),
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 15),
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Container(
                                decoration: BoxDecoration(
                                    color: primary,
                                    borderRadius: BorderRadius.circular(8)),
                                child: const Padding(
                                  padding: EdgeInsets.all(5.0),
                                  child: Icon(
                                    Icons.link_outlined,
                                    color: white,
                                  ),
                                ),
                              ),
                              title: const Text(
                                'Take Photo',
                              ),
                              subtitle: Text(
                                _selfie == null ? 'Take a selfie' : 'Uploaded',
                                style: TextStyle(color: black.withOpacity(.2)),
                              ),
                              trailing: Container(
                                decoration: BoxDecoration(
                                  border:
                                      Border.all(color: black.withOpacity(.1)),
                                  color: white.withOpacity(.1),
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(5),
                                  child: Icon(
                                    Icons.edit,
                                    color: black.withOpacity(.3),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            sizedBox(20, 0),
            CustomButton(
              title: 'Continue',
              onTap: () {
                if (_image == null) {
                  Get.snackbar('Error', 'Please upload a documents to verify.');
                } else if (_selfie == null) {
                  Get.snackbar('Error', 'Please take a selfie to verify.');
                } else {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => SendRequestScreen(
                        name: widget.name.toString(),
                        docPath: _image!.path,
                        selfiePath: _selfie!.path,
                        email: widget.email,
                        dateOfBirth: widget.dateOfBirth,
                        phnNumber: widget.phoneNumber,
                      ),
                    ),
                  );
                }
              },
              bgColor: _image == null || _selfie == null
                  ? Colors.grey.withOpacity(.2)
                  : primary,
            ),
          ],
        ),
      ),
    );
  }
}
