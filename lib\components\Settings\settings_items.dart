import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../utils/colors.dart';
import '../size_box.dart';

Widget settingItem({
  required double size,
  required String title,
  required String icon,
  void Function()? onTap,
}) {
  return InkWell(
    onTap: onTap,
    child: Container(
      width: size * .9,
      decoration:
          BoxDecoration(color: white, borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10.0),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(.1), shape: BoxShape.circle),
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Center(
                  child: SvgPicture.asset(
                    icon,
                    width: 16,
                  ),
                ),
              ),
            ),
            sizedBox(0, 10),
            Text(
              title,
              style: TextStyle(
                  color: black.withOpacity(.5),
                  fontSize: 15,
                  fontWeight: FontWeight.w400),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: black.withOpacity(.5),
              size: 20,
            )
          ],
        ),
      ),
    ),
  );
}
