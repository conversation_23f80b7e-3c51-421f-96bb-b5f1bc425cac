import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/auth/forget_password_controller.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../components/size_box.dart';
import '../../../utils/colors.dart';
import '../../../utils/config.dart';
import '../../../widgets/button_widget.dart';

class ForgetPassScreen extends StatefulWidget {
  const ForgetPassScreen({Key? key}) : super(key: key);

  @override
  State<ForgetPassScreen> createState() => _ForgetPassScreenState();
}

class _ForgetPassScreenState extends State<ForgetPassScreen> {
  static final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    Get.put(ForgetPasswordController());
    final controller = ForgetPasswordController();
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      extendBodyBehindAppBar: false,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: background,
        automaticallyImplyLeading: false,
        elevation: 0,
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: const Icon(
              Icons.arrow_back_ios_outlined,
              color: primary,
            )),
        title: const Text(
          'Forget Password',
          style: TextStyle(
            color: primary,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: size * .9,
                decoration: BoxDecoration(
                  color: white,
                  boxShadow: [
                    BoxShadow(
                      color: black.withOpacity(0.1),
                      blurRadius: 5,
                    )
                  ],
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: Column(
                    children: [
                      Column(
                        children: [
                          Container(
                            height: 100,
                            width: 100,
                            decoration: BoxDecoration(
                              border: Border.all(color: primary, width: 2),
                              color: background,
                              shape: BoxShape.circle,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(20.0),
                              child: Center(
                                child: Lottie.asset(lockJson),
                              ),
                            ),
                          ),
                          sizedBox(20, 0),
                          const Text(
                            'Password Reset',
                            style: TextStyle(
                              color: black,
                              fontWeight: FontWeight.w600,
                              fontSize: 25,
                            ),
                          ),
                          Text(
                            'Enter a email address to receive confirmation code.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: black.withOpacity(0.5),
                              fontWeight: FontWeight.w300,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      sizedBox(30, 0),
                      Form(
                        key: _formKey,
                          child: TextFormField(
                            controller: controller.emailController,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return emailError;
                              }
                              return null;
                            },
                            decoration: InputDecoration(
                              prefixIconColor: black.withOpacity(0.5),
                              suffixIconColor: black.withOpacity(0.5),
                              focusColor: Colors.red,
                              prefixIcon: const Icon(Icons.alternate_email),
                              label: const Text('E-Mail Address'),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: black.withOpacity(0.1),
                                  width: 1.5,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                              border: OutlineInputBorder(
                                borderSide: const BorderSide(
                                  color: black,
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(15),
                              ),
                            ),
                          )),
                      sizedBox(20, 0),
                      Obx((){
                        if(controller.isLoading.value){
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        } else{
                          return CustomButton(
                            onTap: (){
                              if(_formKey.currentState!.validate()){
                                controller.forgetPassword(context: context,email: controller.emailController.text);
                              }
                            },
                            title: 'Recover',
                          );
                        }
                      }),
                    ],
                  ),
                ),
              ),
              sizedBox(50, 0),
            ],
          ),
        ),
      ),
    );
  }
}
