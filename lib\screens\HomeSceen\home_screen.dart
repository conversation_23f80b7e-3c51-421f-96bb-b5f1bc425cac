import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/bottom_bar_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_all_post_controller.dart';
import 'package:flutter_wowonder/screens/FindFriendsScreen/find_friends_screen.dart';
import 'package:flutter_wowonder/screens/MoreScreen/more_option.dart';
import 'package:flutter_wowonder/screens/NotificationScreen/notification_screen.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/add_feed.dart';
import 'package:flutter_wowonder/widgets/app_bar.dart';
import 'package:flutter_wowonder/widgets/post_body.dart';
import 'package:get/get.dart';
import 'package:refresh_loadmore/refresh_loadmore.dart';
import '../../components/modern_bar.dart';
import '../../controllers/UserData/get_user_data_controller.dart';
import '../../widgets/user_stories_section.dart';
import '../ReelScreen/reel_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final isLiked = false;
  final profileDetails = Get.put(UserDataController());

  @override
  Widget build(BuildContext context) {
    final postController = Get.put(GetAllPostController());
    final BottomBarController controller =
        Get.put(BottomBarController(), permanent: false);
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: customAppBar(size),
      body: Obx(
        () => Column(
          children: [
            Expanded(
              child: IndexedStack(
                index: controller.tabIndex.value,
                children: [
                  RefreshLoadmore(
                    onRefresh: postController.onRefresh,
                    onLoadmore: postController.onLoading,
                    isLastPage: false,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const AddFeed(),
                          sizedBox(10, 0),
                          const UserStory(),
                          sizedBox(10, 0),
                          const PostData()
                        ],
                      ),
                    ),
                  ),
                  ReelScreen(
                    size: size,
                  ),
                  const FindFriendsScreen(),
                  const NotificationScreen(),
                  const MoreOptionScreen(),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const ModernBar(),
    );
  }
}
