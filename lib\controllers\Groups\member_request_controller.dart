import '../../models/Groups/member_request_model.dart';
import '../../services/api_services.dart';
import 'package:get/get.dart';

class MemberRequestController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var requestData = MemberRequestModel().obs;

  getData({required String groupID}) async {
    isLoading.value = true;
    var apiData = await APISERvices().memberRequest(groupID: groupID);
    requestData.value = apiData!;
    isLoading.value = false;
    update();
  }

  setAction ({required String userID, required int index, required String groupID, required String type}) async {
    await APISERvices.acceptRequest(groupID: groupID, userID: userID, type: type);
    requestData.value.data!.removeAt(index);
    update();
    requestData.refresh();
  }

}