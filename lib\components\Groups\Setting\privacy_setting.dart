import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Settings/privacy_items.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/Groups/group_details_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:get/get.dart';

class GroupPrivacy extends StatefulWidget {
  final String groupID;

  const GroupPrivacy({Key? key, required this.groupID}) : super(key: key);

  @override
  State<GroupPrivacy> createState() => _GroupPrivacyState();
}

class _GroupPrivacyState extends State<GroupPrivacy> {
  final controller = Get.put(GroupDetailsController());
  String privacy = '';
  String joiningPrivacy = '';

  getValue() {
    final data = controller.groupData.value;
    privacy = data.privacy!;
    joiningPrivacy = data.joinPrivacy!;
  }

  @override
  void initState() {
    getValue();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Privacy'),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            sizedBox(10, 0),
            privacyItems(
              size,
              'Group Type',
              privacy == '1' ? 'Public' : 'Private',
              () {
                Get.defaultDialog(
                    title: 'Privacy',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              privacy = '1';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: Center(
                                child: Text('Public'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              privacy = '2';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: Center(
                                child: Text('Private'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              },
            ),
            sizedBox(10, 0),
            privacyItems(
              size,
              'Confirm request when someone joining this group?',
              joiningPrivacy != '1' ? 'Yes' : 'No',
              () {
                Get.defaultDialog(
                    title: 'Joining Privacy',
                    content: Column(
                      children: [
                        InkWell(
                          onTap: () {
                            setState(() {
                              joiningPrivacy = '2';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: Center(
                                child: Text('Yes'),
                              ),
                            ),
                          ),
                        ),
                        sizedBox(10, 0),
                        InkWell(
                          onTap: () {
                            setState(() {
                              joiningPrivacy = '1';
                            });
                            Navigator.pop(context);
                          },
                          child: Container(
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: Colors.grey.withOpacity(.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10.0),
                              child: Center(
                                child: Text('No'),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ));
              },
            ),
            sizedBox(10, 0),
            CustomButton(
              title: 'Save',
              onTap: () {
                APISERvices.updateGroupPrivacy(
                  groupID: widget.groupID,
                  context: context,
                  privacy: privacy,
                  joiningPrivacy: joiningPrivacy,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
