import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/PostModels/get_page_post_model.dart';

class GetPagePostController extends GetxController{
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  RxList<PageDatum> postData = <PageDatum>[].obs;
  getData ({required pageID}) async {
    isLoading.value == true;
    final dataList = await apiServices.getPagePost(pageID: pageID);
    postData.value = dataList!.data!;
    isLoading.value == false;
    update();
  }
  statusChange(String id, String status, PageDatum getCountries, int index,
      {String? ccc}) async {
    await apiServices.postReaction(postID: id, action: status, count: ccc);

    getCountries.setIsActive(ccc.toString());
    postData[index] = getCountries;
    update();
  }
}
