// To parse this JSON data, do
//
//     final recentSearchModel = recentSearchModelFromJson(jsonString);

import 'dart:convert';

RecentSearchModel recentSearchModelFromJson(String str) => RecentSearchModel.fromJson(json.decode(str));


class RecentSearchModel {
  int? apiStatus;
  List<Datum>? data;

  RecentSearchModel({
    this.apiStatus,
    this.data,
  });

  factory RecentSearchModel.fromJson(Map<String, dynamic> json) => RecentSearchModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
  );

}

class Datum {
  String? userID;
  String? avatar;
  String? name;
  String? groupID;
  String? pageID;

  Datum({
    this.userID,
    this.avatar,
    this.name,
    this.groupID,
    this.pageID
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
    userID: json["user_id"] ?? '',
    avatar: json["avatar"],
    name: json["name"],
    groupID: json['group_id'] ?? '',
    pageID: json['page_id'] ?? ''

  );


}
