// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import '../controllers/bottom_bar_controller.dart';

BottomNavigationBarItem modernBarItems(
    BottomBarController controller,
    String selectedIcon,
    String unSelectedIcon,
    String label,
    Color selectedColor,
    Color unSelectedColor,
    int index) {
  return BottomNavigationBarItem(
    icon: controller.tabIndex.value == index
        ? SvgPicture.asset(
            selectedIcon,
            color: selectedColor,
          )
        : SvgPicture.asset(
            unSelectedIcon,
            color: unSelectedColor,
          ),
    label: label,
    backgroundColor: Colors.transparent,
  );
}

BottomNavigationBarItem pngModernBarItems(
    BottomBarController controller,
    String selectedIcon,
    String unSelectedIcon,
    String label,
    Color selectedColor,
    Color unSelectedColor,
    int index,
    ) {
  return BottomNavigationBarItem(
    icon: controller.tabIndex.value == index
        ? SvgPicture.asset(
      selectedIcon,
      color: selectedColor,
      width: 27,
    )
        : SvgPicture.asset(
      unSelectedIcon,
      color: unSelectedColor,
      width: 27,
    ),
    label: label,
    backgroundColor: Colors.transparent,
  );

}
BottomNavigationBarItem pngBarItem(
    BottomBarController controller,
    String selectedIcon,
    String unSelectedIcon,
    String label,
    Color selectedColor,
    Color unSelectedColor,
    int index,
    ) {
  return BottomNavigationBarItem(
    icon: controller.tabIndex.value == index
        ? Image.asset(
      selectedIcon,
      color: selectedColor,
      width: 27,
    )
        : Image.asset(
      unSelectedIcon,
      color: unSelectedColor,
      width: 27,
    ),
    label: label,
    backgroundColor: Colors.transparent,
  );
}
