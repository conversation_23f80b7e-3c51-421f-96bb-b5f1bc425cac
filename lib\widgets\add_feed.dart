import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_all_post_controller.dart';
import 'package:get/get.dart';

import '../components/Shimmer/shimmer_effect.dart';
import '../components/size_box.dart';
import '../utils/colors.dart';
import '../utils/config.dart';

class AddFeed extends GetView<MyDataController> {
  const AddFeed({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(MyDataController());
    var postController = Get.put(GetAllPostController());
    return InkWell(
      onTap: () async {
        var result = await navigate('newPost');
        if (result != null) {
          if (result == true) {
            postController.onRefresh();
          }
        }
      },
      child: Container(
        height: 180,
        decoration: BoxDecoration(
            boxShadow: [BoxShadow(color: black.withOpacity(0.1), blurRadius: 10)],
            color: white,
            borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(30),
                bottomRight: Radius.circular(30))),
        child: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Divider(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(() {
                    if (controller.isLoading.value) {
                      return homeProfileShimmer();
                    } else {
                      final data = controller.getUserDetails.value;
                      return data.avatar == null ? homeProfileShimmer() :
                      Container(
                        height: 35,
                        width: 35,
                        decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            image: DecorationImage(
                                image: NetworkImage(data.avatar!),
                                fit: BoxFit.cover)),
                      );
                    }
                  }),
                  Center(
                    child: Text(
                      inMind,
                    ),
                  ),
                  Container(
                    height: 20,
                    width: 20,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: SvgPicture.asset(
                      'assets/svg/smile.svg',
                    ),
                  ),
                ],
              ),
              const Divider(),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/camera_icon.svg',
                                  width: 17,
                                ),
                                Text(
                                  'Photos',
                                  style: TextStyle(
                                      color: black.withOpacity(0.5), fontSize: 13),
                                )
                              ],
                            ),
                          ),
                        ),
                      )),
                  sizedBox(0, 5),
                  Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/video_icon.svg',
                                  width: 17,
                                ),
                                Text(
                                  'Videos',
                                  style: TextStyle(
                                      color: black.withOpacity(0.5), fontSize: 13),
                                )
                              ],
                            ),
                          ),
                        ),
                      )),
                  sizedBox(0, 5),
                  Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/smile.svg',
                                  width: 17,
                                ),
                                Text(
                                  'Feelings',
                                  style: TextStyle(
                                      color: black.withOpacity(0.5), fontSize: 13),
                                )
                              ],
                            ),
                          ),
                        ),
                      )),
                ],
              ),
              sizedBox(10, 0),
            ],
          ),
        ),
      ),
    );
  }
}

