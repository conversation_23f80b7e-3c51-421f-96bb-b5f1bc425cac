import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:flutter_wowonder/screens/PagesScreen/page_details_screen.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:get/get.dart';
import '../../components/Shimmer/shimmer_effect.dart';
import '../../components/size_box.dart';
import '../../controllers/Pages/get_pages_controller.dart';
import '../../widgets/Pages/app_bar.dart';

class PageListScreen extends StatefulWidget {
  const PageListScreen({Key? key}) : super(key: key);

  @override
  State<PageListScreen> createState() => _PageListScreenState();
}

class _PageListScreenState extends State<PageListScreen>
    with TickerProviderStateMixin {
  final myPageController = Get.put(GetMyPageController());
  final likedPageController = Get.put(GetLikedPagesController());
  final profileData = Get.put(MyDataController());
  final recommendedController = Get.put(RecommendedPagesController());
  TabController? tabController;


  @override
  void initState() {
    tabController = TabController(
      initialIndex: 0,
      length: 3,
      vsync: this,
    );
    String userID = profileData.getUserDetails.value.userId!;
    myPageController.getData(type: 'my_pages');
    likedPageController.getData(type: 'liked_pages', userID: userID);
    recommendedController.getData(type: 'pages');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final shimmerSize = MediaQuery.of(context).size;
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(
          bgColor: white,
          title: 'Pages',
          bottom: TabBar(
            isScrollable: false,
            indicatorColor: primary,
            controller: tabController,
            tabs: const [
              Tab(
                child: Text(
                  'My Pages',
                  style: TextStyle(color: black),
                ),
              ),
              Tab(
                child: Text(
                  'Liked Pages',
                  style: TextStyle(color: black),
                ),
              ),
              Tab(
                child: Text(
                  'Recommended',
                  style: TextStyle(color: black, fontSize: 11),
                ),
              ),
            ],
          ),
      ),
      body: TabBarView(
        controller: tabController,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 15.0,
              vertical: 10,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  sizedBox(10, 0),
                  Obx(() {
                    if (myPageController.isLoading.value) {
                      return searchResultShimmer(shimmerSize);
                    } else {
                      return myPageController.myPagesData.value!.isEmpty
                          ? const Center(
                              child: Text('You haven\'t created any page yet'),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              primary: false,
                              itemCount:
                                  myPageController.myPagesData.value!.length,
                              itemBuilder: (_, index) {
                                final data =
                                    myPageController.myPagesData.value![index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Container(
                                    width: shimmerSize.width * .9,
                                    decoration: BoxDecoration(
                                      boxShadow: [
                                        BoxShadow(
                                            color: black.withOpacity(.100),
                                            blurRadius: 1)
                                      ],
                                      color: white,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 15.0,
                                        vertical: 8,
                                      ),
                                      child: Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 30,
                                            backgroundImage:
                                                NetworkImage(data.avatar!),
                                          ),
                                          sizedBox(0, 10),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    data.name!,
                                                    style: const TextStyle(
                                                        color: black,
                                                        fontSize: 19,
                                                        fontWeight:
                                                            FontWeight.w400),
                                                  ),
                                                  data.verified == '1'
                                                      ? SvgPicture.asset(
                                                          'assets/svg/verify.svg',
                                                          width: 18,
                                                        )
                                                      : Container(),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    data.category!,
                                                    style: TextStyle(
                                                      color:
                                                          black.withOpacity(.5),
                                                    ),
                                                  ),
                                                  Text(
                                                    ' • ',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  ),
                                                  Text(
                                                    '${data.likes} likes',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  )
                                                ],
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (_) =>
                                                          PageDetailsScreen(
                                                        pageID: data.pageId!,
                                                      ),
                                                    ),
                                                  );
                                                },
                                                child: Container(
                                                  width: shimmerSize.width * .6,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color: primary),
                                                  child: const Padding(
                                                    padding: EdgeInsets
                                                            .symmetric(
                                                        vertical: 10.0),
                                                    child: Center(
                                                      child: Text(
                                                        'View Page',
                                                        style: TextStyle(
                                                            color: white),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              });
                    }
                  }),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 15.0,
              vertical: 10,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  sizedBox(10, 0),
                  Obx(() {
                    if (likedPageController.isLoading.value) {
                      return searchResultShimmer(shimmerSize);
                    } else {
                      return likedPageController.likedPageData.value!.isEmpty
                          ? const Center(child: Text('No pages you liked'))
                          : ListView.builder(
                              shrinkWrap: true,
                              primary: false,
                              itemCount: likedPageController
                                  .likedPageData.value!.length,
                              itemBuilder: (_, index) {
                                final data = likedPageController
                                    .likedPageData.value![index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Container(
                                    width: shimmerSize.width * .9,
                                    decoration: BoxDecoration(
                                      boxShadow: [
                                        BoxShadow(
                                            color: black.withOpacity(.100),
                                            blurRadius: 1)
                                      ],
                                      color: white,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 15.0,
                                        vertical: 8,
                                      ),
                                      child: Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 30,
                                            backgroundImage:
                                                NetworkImage(data.avatar!),
                                          ),
                                          sizedBox(0, 10),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    data.name!,
                                                    style: const TextStyle(
                                                        color: black,
                                                        fontSize: 19,
                                                        fontWeight:
                                                            FontWeight.w400),
                                                  ),
                                                  data.verified == '1'
                                                      ? SvgPicture.asset(
                                                          'assets/svg/verify.svg',
                                                          width: 18,
                                                        )
                                                      : Container(),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    data.category!,
                                                    style: TextStyle(
                                                      color:
                                                          black.withOpacity(.5),
                                                    ),
                                                  ),
                                                  Text(
                                                    ' • ',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  ),
                                                  Text(
                                                    '${data.likes} likes',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  )
                                                ],
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (_) =>
                                                          PageDetailsScreen(
                                                        pageID: data.pageId!,
                                                      ),
                                                    ),
                                                  );
                                                },
                                                child: Container(
                                                  width: shimmerSize.width * .6,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color: primary),
                                                  child: const Padding(
                                                    padding: EdgeInsets
                                                            .symmetric(
                                                        vertical: 10.0),
                                                    child: Center(
                                                      child: Text(
                                                        'View Page',
                                                        style: TextStyle(
                                                            color: white),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              });
                    }
                  }),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 15.0,
              vertical: 10,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  sizedBox(10, 0),
                  Obx(() {
                    if (recommendedController.isLoading.value) {
                      return searchResultShimmer(shimmerSize);
                    } else {
                      return recommendedController
                              .recommendedPagesData.value!.isEmpty
                          ? const Center(
                              child: Text('No recommendation for you'),
                            )
                          : ListView.builder(
                              shrinkWrap: true,
                              primary: false,
                              itemCount: recommendedController
                                  .recommendedPagesData.value!.length,
                              itemBuilder: (_, index) {
                                final data = recommendedController
                                    .recommendedPagesData.value![index];
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 10.0),
                                  child: Container(
                                    width: shimmerSize.width * .9,
                                    decoration: BoxDecoration(
                                      boxShadow: [
                                        BoxShadow(
                                            color: black.withOpacity(.100),
                                            blurRadius: 1)
                                      ],
                                      color: white,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 15.0,
                                        vertical: 8,
                                      ),
                                      child: Row(
                                        children: [
                                          CircleAvatar(
                                            radius: 30,
                                            backgroundImage:
                                                NetworkImage(data.avatar!),
                                          ),
                                          sizedBox(0, 10),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    data.name!,
                                                    style: const TextStyle(
                                                        color: black,
                                                        fontSize: 19,
                                                        fontWeight:
                                                            FontWeight.w400),
                                                  ),
                                                  data.verified == '1'
                                                      ? SvgPicture.asset(
                                                          'assets/svg/verify.svg',
                                                          width: 18,
                                                        )
                                                      : Container(),
                                                ],
                                              ),
                                              Row(
                                                children: [
                                                  Text(
                                                    data.category!,
                                                    style: TextStyle(
                                                      color:
                                                          black.withOpacity(.5),
                                                    ),
                                                  ),
                                                  Text(
                                                    ' • ',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  ),
                                                  Text(
                                                    '${data.likes} likes',
                                                    style: TextStyle(
                                                        color: black
                                                            .withOpacity(.5)),
                                                  )
                                                ],
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (_) =>
                                                          PageDetailsScreen(
                                                        pageID: data.pageId!,
                                                      ),
                                                    ),
                                                  );
                                                },
                                                child: Container(
                                                  width: shimmerSize.width * .6,
                                                  decoration: BoxDecoration(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              10),
                                                      color: primary),
                                                  child: const Padding(
                                                    padding: EdgeInsets
                                                            .symmetric(
                                                        vertical: 10.0),
                                                    child: Center(
                                                      child: Text(
                                                        'View Page',
                                                        style: TextStyle(
                                                            color: white),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              });
                    }
                  }),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
