import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/Pages/pages_details_controller.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:flutter_wowonder/widgets/form_helper.dart';
import 'package:get/get.dart';
import '../../../utils/colors.dart';

class PageGeneralSetting extends StatelessWidget {
  final String pageID;
  const PageGeneralSetting({Key? key, required this.pageID}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final data = Get.put(PageDetailsController());
    final pageData = data.pageData.value;
    final pageNameController = TextEditingController();
    final userNameController = TextEditingController();
    pageNameController.text = pageData.name!;
    userNameController.text = pageData.username!;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'General'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              sizedBox(10, 0),
              sizedBox(20, 0),
              const Text(
                'Basic Details',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              sizedBox(10, 0),
              Form(
                  child: Column(children: [
                customFieldHelper(
                    controller: pageNameController,
                    prefixIcon: Icons.person,
                    label: 'Page Name'),
                sizedBox(15, 0),
                customFieldHelper(
                  controller: userNameController,
                  prefixIcon: Icons.alternate_email,
                  label: 'Page Username',
                ),
              ],)),
              sizedBox(15, 0),
              CustomButton(
                title: 'Save',
                onTap: () async {
                  APISERvices.updatePageGeneral(
                    pageID: pageID,
                    context: context,
                    pageName: pageNameController.text.toString(),
                    userName: userNameController.text.toString()
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
