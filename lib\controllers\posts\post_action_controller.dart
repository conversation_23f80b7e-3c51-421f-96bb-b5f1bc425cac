import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

class PostActionController extends GetxController {
  var isLoading = false.obs;
  var reportRes = {}.obs;
  APISERvices apiServices = APISERvices();

  postAction({required String postID, required String action}) async {
    isLoading.value = true;
    var apiData = await apiServices.postAction(postID: postID, action: action);
    reportRes.value = apiData!;
    isLoading.value = false;
    update();
  }

  hidePost({required String postID}) async {
    isLoading.value == true;
    var apiData = await apiServices.hidePost(postID: postID);
    reportRes.value = apiData!;
    update();
  }
}
