// To parse this JSON data, do
//
//     final memberRequestModel = memberRequestModelFromJson(jsonString);

import 'dart:convert';

MemberRequestModel memberRequestModelFromJson(String str) => MemberRequestModel.fromJson(json.decode(str));

String memberRequestModelToJson(MemberRequestModel data) => json.encode(data.toJson());

class MemberRequestModel {
  int? apiStatus;
  List<Datum>? data;

  MemberRequestModel({
    this.apiStatus,
    this.data,
  });

  factory MemberRequestModel.fromJson(Map<String, dynamic> json) => MemberRequestModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "data": data == null ? [] : List<dynamic>.from(data!.map((x) => x.toJson())),
  };
}

class Datum {
  String? id;
  UserData? userData;

  Datum({
    this.id,
    this.userData,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
    id: json["id"],
    userData: json["user_data"] == null ? null : UserData.fromJson(json["user_data"]),
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "user_data": userData?.toJson(),
  };
}

class UserData {
  String? userId;
  String? avatar;
  String? name;
  String? isVerified;
  String? isPro;

  UserData({
    this.userId,
    this.avatar,
    this.name,
    this.isVerified,
    this.isPro
  });

  factory UserData.fromJson(Map<String, dynamic> json) => UserData(
    userId: json["user_id"],
    avatar: json["avatar"],
    name: json["name"],
    isVerified: json['verified'],
    isPro: json['is_pro'],
  );

  Map<String, dynamic> toJson() => {
    "user_id": userId,
    "avatar": avatar,
    "name": name,
  };
}
