import 'package:get/get.dart';
import '../../models/Pages/pages_details_model.dart';
import '../../services/api_services.dart';

class PageDetailsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var pageData = PageData().obs;

  getData({required String pageID}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getPageData(pageID: pageID);
    pageData.value = apiData!.pageData!;
    isLoading.value = false;
    update();
  }

  statusChange(String pageID, bool  status, PageData getData) async {
    await APISERvices.pageLike(pageID: pageID);
    getData.setIsLiked(status);
    pageData.value.isLiked = getData.isLiked;
    update();
    pageData.refresh();
  }
}