import 'dart:convert';

PostCommentModel postCommentModelFromJson(String str) => PostCommentModel.fromJson(json.decode(str));


class PostCommentModel {
  int? apiStatus;
  List<CommentData>? data;


  PostCommentModel({
    this.apiStatus,
    this.data,

  });

  factory PostCommentModel.fromJson(Map<String, dynamic> json) => PostCommentModel(
    apiStatus: json["api_status"],
    data: json["data"] == null ? [] : List<CommentData>.from(json["data"]!.map((x) => CommentData.fromJson(x))),
  );

}

class CommentData {
  String? commentID;
  String? id;
  String? text;
  String? commentTime;
  String? pubAvatar;
  String? pubName;
  String? pubId;
  String? isVerified;
  String? isPro;
  String? reactType;
  int? reactionCount;
  String? replayCount;
  bool? isReacted;
  bool? isAuthor;

  CommentData({
    this.commentID,
    this.text,
    this.commentTime,
    this.pubAvatar,
    this.pubName,
    this.pubId,
    this.isPro,
    this.isVerified,
    this.reactType,
    this.reactionCount,
    this.replayCount,
    this.isReacted,
    this.isAuthor,
  });

  void setIsActive(String value) {
    reactType = value;
  }


  factory CommentData.fromJson(Map<String, dynamic> json) => CommentData(
    commentID: json["id"],
    text: json["text"],
    commentTime: json['time'],
    pubAvatar: json['publisher']['avatar'],
    pubName: json['publisher']['name'],
    pubId: json['publisher']['user_id'],
    isVerified: json['publisher']['verified'],
    isPro: json['publisher']['is_pro'],
    reactType: json['reaction']['type'],
    reactionCount: json['reaction']['count'],
    replayCount: json['replies_count'],
    isReacted: json['reaction']['is_reacted'],
    isAuthor: json['onwer'],
  );
}
