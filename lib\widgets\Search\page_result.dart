import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/screens/PagesScreen/page_details_screen.dart';
import 'package:get/get.dart';

import '../../components/Shimmer/shimmer_effect.dart';
import '../../components/home/<USER>';
import '../../components/size_box.dart';
import '../../controllers/Search/search_controller.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';

class PageSearchResults extends GetView<SearchResultsController> {
  const PageSearchResults({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(SearchResultsController());
    final size = MediaQuery.of(context).size;
    return Obx(() {
      if (controller.isLoading.value) {
        return searchResultShimmer(size);
      } else {
        if (controller.searchData.value.pages!.isEmpty) {
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  noData,
                  width: size.width * .5,
                ),
                const Text(
                  'No data!',
                  style: TextStyle(color: primary, fontSize: 30),
                ),
                const Text('There are no data available!')
              ],
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.all(10.0),
            child: ListView.builder(
                shrinkWrap: true,
                primary: false,
                itemCount: controller.searchData.value.pages!.length,
                itemBuilder: (_, index) {
                  final data = controller.searchData.value.pages![index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: Column(
                      children: [
                        data.boosted == '1'
                            ? boostedBadge(size.width)
                            : Container(),
                        Container(
                          width: size.width * .950,
                          decoration: BoxDecoration(
                            boxShadow: [
                              BoxShadow(
                                  color: black.withOpacity(.100), blurRadius: 1)
                            ],
                            color: white,
                            borderRadius: data.boosted == '1'
                                ? const BorderRadius.only(
                              bottomRight: Radius.circular(20),
                              bottomLeft: Radius.circular(20)
                            )
                                : BorderRadius.circular(20),
                            border: data.boosted == '1' ? Border.all(
                              color: primary
                            ) : Border.all(
                              color: Colors.transparent
                            )
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 15.0,
                              vertical: 8,
                            ),
                            child: Row(
                              children: [
                                CircleAvatar(
                                  radius: 30,
                                  backgroundImage: NetworkImage(data.avatar!),
                                ),
                                sizedBox(0, 10),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          data.name!,
                                          style: const TextStyle(
                                              color: black,
                                              fontSize: 19,
                                              fontWeight: FontWeight.w400),
                                        ),
                                        data.verified == '1'
                                            ? SvgPicture.asset(
                                                'assets/svg/verify.svg',
                                                width: 18,
                                              )
                                            : Container(),
                                      ],
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                          data.category!,
                                          style: TextStyle(
                                            color: black.withOpacity(.5),
                                          ),
                                        ),
                                      ],
                                    ),
                                    InkWell(
                                      onTap: (){
                                        pageRoute(context, PageDetailsScreen(pageID: controller.searchData.value.pages![index].pageId!));
                                      },
                                      child: Container(
                                        width: size.width * .650,
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(10),
                                            color: primary),
                                        child: const Padding(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 10.0),
                                          child: Center(
                                            child: Text(
                                              'View Page',
                                              style: TextStyle(color: white),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
          );
        }
      }
    });
  }
}
