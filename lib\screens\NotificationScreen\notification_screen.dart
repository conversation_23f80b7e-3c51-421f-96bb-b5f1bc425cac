import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/controllers/Notification/notification_controller.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import '../../utils/colors.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({Key? key}) : super(key: key);

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final controller = Get.put(NotificationController());

  @override
  void initState() {
    controller.getData();
    super.initState();
  }

  void refresh() {
    controller.getData();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: background,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              sizedBox(10, 0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Notifications',
                    style: TextStyle(
                        color: black.withOpacity(0.5),
                        fontSize: 22,
                        fontWeight: FontWeight.w600),
                  ),
                  IconButton(
                    onPressed: () {
                      controller.getData();
                    },
                    icon: Icon(
                      Icons.refresh,
                      color: black.withOpacity(.5),
                    ),
                  )
                ],
              ),
              Divider(
                color: black.withOpacity(.1),
              ),
              Obx(
                () {
                  if (controller.isLoading.value) {
                    return searchResultShimmer(size);
                  } else {
                    return ListView.builder(
                      itemCount: controller
                          .notificationData.value.notifications!.length,
                      shrinkWrap: true,
                      primary: false,
                      itemBuilder: (_, index) {
                        final data = controller
                            .notificationData.value.notifications![index];
                        return Container(
                          margin: const EdgeInsets.only(bottom: 10),
                          width: size.width * .9,
                          decoration: BoxDecoration(
                              color: white.withOpacity(.8),
                              borderRadius: BorderRadius.circular(15),
                              border: Border.all(color: black.withOpacity(.1))),
                          child: ListTile(
                            leading: data.notifiedVerified == '1'
                                ? Stack(
                                    alignment: AlignmentDirectional.bottomEnd,
                                    children: [
                                      CircleAvatar(
                                        backgroundImage:
                                            NetworkImage(data.notifierAvatar!),
                                      ),
                                      SvgPicture.asset(
                                        'assets/svg/verify.svg',
                                        width: 18,
                                      )
                                    ],
                                  )
                                : CircleAvatar(
                                    backgroundImage:
                                        NetworkImage(data.notifierAvatar!),
                                  ),
                            title: RichText(
                              text: TextSpan(
                                text: data.notifierName,
                                style: const TextStyle(
                                    color: primary,
                                    fontWeight: FontWeight.bold),
                                children: [
                                  TextSpan(
                                    text: ' ',
                                    style: TextStyle(
                                        color: black.withOpacity(.5),
                                        fontWeight: FontWeight.w400),
                                  ),
                                  TextSpan(
                                    text: data.typeText,
                                    style: TextStyle(
                                        color: black.withOpacity(.5),
                                        fontWeight: FontWeight.w400),
                                  )
                                ],
                              ),
                            ),
                            subtitle: Text(data.timeTextString!),
                          ),
                        );
                      },
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
