<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="108" height="108" viewBox="0 0 108 108">
  <defs>
    <filter id="Ellipse_27" x="0" y="0" width="108" height="108" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="15" result="blur"/>
      <feFlood flood-color="#657190" flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Group_105" data-name="Group 105" transform="translate(44.732 42)">
    <g transform="matrix(1, 0, 0, 1, -44.73, -42)" filter="url(#Ellipse_27)">
      <circle id="Ellipse_27-2" data-name="Ellipse 27" cx="9" cy="9" r="9" transform="translate(45 42)" fill="#2d3f7b"/>
    </g>
    <path id="Icon_ionic-ios-moon" data-name="Icon ionic-ios-moon" d="M11.592,9.885c-.054,0-.108,0-.162,0A3.273,3.273,0,0,1,9.076,8.9,3.382,3.382,0,0,1,8.1,6.506a3.425,3.425,0,0,1,.325-1.461,4.839,4.839,0,0,1,.252-.434A.073.073,0,0,0,8.607,4.5a3.591,3.591,0,0,0,.548,7.129,3.5,3.5,0,0,0,2.69-1.264,3.391,3.391,0,0,0,.3-.4.075.075,0,0,0-.076-.113A3.171,3.171,0,0,1,11.592,9.885Z" transform="translate(-0.078 0.756)" fill="#fff"/>
  </g>
</svg>
