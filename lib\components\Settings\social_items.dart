import 'package:flutter/material.dart';

import '../../utils/colors.dart';

Widget socialItems({
  required String title,
  required double size,
  TextEditingController? controller,
}) {
  return Container(
    width: size * .9,
    decoration: BoxDecoration(
      border: Border.all(
        color: black.withOpacity(.1),
      ),
      borderRadius: BorderRadius.circular(15),
    ),
    child: Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 15.0,
      ),
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          border: InputBorder.none,
          label: Text(title),
        ),
      ),
    ),
  );
}
