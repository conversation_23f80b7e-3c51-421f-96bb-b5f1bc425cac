// ignore_for_file: depend_on_referenced_packages

import 'package:better_player/better_player.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:path/path.dart' as path;
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/home/<USER>';
import '../../components/size_box.dart';
import '../../controllers/posts/post_action_controller.dart';
import '../../controllers/posts/saved_post_controller.dart';
import '../../widgets/button_widget.dart';

class SavedPostScreen extends StatefulWidget {
  const SavedPostScreen({Key? key}) : super(key: key);

  @override
  State<SavedPostScreen> createState() => _SavedPostScreenState();
}

class _SavedPostScreenState extends State<SavedPostScreen> {
  final controller = Get.put(SavedPostController());
  final PageController pageController = PageController();

  @override
  void initState() {
    controller.getData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final postActionController = Get.put(PostActionController());
    final size = MediaQuery.of(context).size.width;
    Get.put(SavedPostController());

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Saved'),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        } else {
          return controller.dataList.isEmpty
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Lottie.asset('assets/json/empty.json', width: size * 4),
                    Text(
                      'Empty!',
                      style: TextStyle(
                          color: black.withOpacity(.5),
                          fontSize: 40,
                          fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'There are no saved post',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: black.withOpacity(.5),
                        fontSize: 16,
                      ),
                    ),
                    sizedBox(20, 0),
                    CustomButton(
                      title: 'Refresh',
                      onTap: () {
                        controller.getData();
                      },
                    ),
                  ],
                )
              : Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: ListView.builder(
                      padding: EdgeInsets.zero,
                      primary: false,
                      shrinkWrap: true,
                      itemCount: controller.dataList.length,
                      itemBuilder: (_, index) {
                        var data = controller.dataList[index];
                        final fileType = path.extension(data.postFile!);
                        return Column(
                          children: [
                            data.isBoosted == 1
                                ? boostedBadge(size)
                                : Container(),
                            Container(
                              margin: const EdgeInsets.only(bottom: 20),
                              decoration: BoxDecoration(
                                  color: white,
                                  borderRadius: data.isBoosted == 1
                                      ? const BorderRadius.only(
                                          bottomRight: Radius.circular(20),
                                          bottomLeft: Radius.circular(20),
                                        )
                                      : BorderRadius.circular(20)),
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: Center(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      data.postType == "profile_cover_picture"
                                          ? postType()
                                          : Container(),
                                      //publisher start
                                      mainPublisher(controller.dataList[index],
                                          size, context, postActionController),
                                      //publisher end
                                      data.postFeeling != ''
                                          ? postFeeling(data)
                                          : Container(),
                                      const Divider(),
                                      sizedBox(5, 0),
                                      data.postText == ''
                                          ? Container()
                                          : Html(data: data.postText!),
                                      sizedBox(5, 0),
                                      data.sharedInfo == null
                                          ? Container()
                                          : sharedInfo(data, size),
                                      data.multiImage == '1'
                                          ? multiImage(
                                              data, size, pageController)
                                          : Container(),
                                      data.postFile!.isNotEmpty
                                          ? fileType == '.mp4' ||
                                                  fileType == '.m4v' ||
                                                  fileType == '.webm' ||
                                                  fileType == '.flv' ||
                                                  fileType == '.mov' ||
                                                  fileType == '.mpeg' ||
                                                  fileType == '.mkv'
                                              ? AspectRatio(
                                                  aspectRatio: 16 / 9,
                                                  child: BetterPlayer.network(
                                                    data.postFile!,
                                                    betterPlayerConfiguration:
                                                        const BetterPlayerConfiguration(
                                                      aspectRatio: 16 / 9,
                                                    ),
                                                  ),
                                                )
                                              : fileType == '.jpg' ||
                                                      fileType == '.jpeg' ||
                                                      fileType == '.png' ||
                                                      fileType == '.gif'
                                                  ? InkWell(
                                                      onTap: () {
                                                        showDialog(
                                                            context: context,
                                                            builder: (_) => AlertDialog(
                                                              insetPadding: EdgeInsets.zero,
                                                              contentPadding: EdgeInsets.zero,
                                                              clipBehavior: Clip.antiAliasWithSaveLayer,
                                                              shape: const RoundedRectangleBorder(
                                                                  borderRadius:
                                                                  BorderRadius.all(Radius.circular(10.0))),
                                                              content: Builder(builder: (context) {
                                                                return Image.network(data.postFile!,
                                                                  fit: BoxFit.cover,
                                                                );
                                                              }),
                                                            ));
                                                      },
                                                      child: Container(
                                                        height: 250,
                                                        width: size * .9,
                                                        decoration:
                                                            BoxDecoration(
                                                                color:
                                                                    Colors.grey,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                            20),
                                                                image:
                                                                    DecorationImage(
                                                                  image: NetworkImage(
                                                                      data.postFile!),
                                                                  fit: BoxFit
                                                                      .cover,
                                                                )),
                                                      ),
                                                    )
                                                  : Container()
                                          : Container(),
                                      sizedBox(10, 0),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          postMeta(data),
                                          const Divider(),
                                          postAction(
                                              controller, data, index, context),
                                        ],
                                      ),
                                      sizedBox(20, 0),
                                      data.getPostComments!.isNotEmpty
                                          ? postComment(data, context)
                                          : Container(),
                                      data.getPostComments!.isNotEmpty
                                          ? sizedBox(20, 0)
                                          : Container(),
                                      data.isComment == "0"
                                          ? commentInactive(size)
                                          : CommentActive(data),
                                      sizedBox(10, 0),
                                    ],
                                  ),
                                ),
                              ),
                            )
                          ],
                        );
                      }),
                );
        }
      }),
    );
  }
}
