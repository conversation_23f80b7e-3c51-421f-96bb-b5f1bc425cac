import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/size_box.dart';
import '../../controllers/Search/search_controller.dart';
import '../../utils/colors.dart';
import '../../widgets/Search/user_result.dart';

class FindFriendsScreen extends StatefulWidget {
  const FindFriendsScreen({Key? key}) : super(key: key);

  @override
  State<FindFriendsScreen> createState() => _FindFriendsScreenState();
}

class _FindFriendsScreenState extends State<FindFriendsScreen> {
  final controller = Get.put(SearchResultsController());

  @override
  void initState() {
    super.initState();
    controller.getData(searchText: '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: background,
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            sizedBox(10, 0),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 15.0,
              ),
              child: Text(
                'Find Friends',
                style: TextStyle(
                    color: black.withOpacity(0.5),
                    fontSize: 22,
                    fontWeight: FontWeight.w600),
              ),
            ),
            sizedBox(5, 0),
            const UserSearchResults()
          ],
        ),
      ),
    );
  }
}
