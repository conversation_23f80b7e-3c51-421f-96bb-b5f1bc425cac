import 'package:flutter/material.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Container(
        color: primary,
        child: Center(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                appIcon,
                color: white,
                width: size * .6,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
