import 'package:flutter/material.dart';

import '../../utils/colors.dart';

Widget privacyItems (size, String title, String value, void Function() onTap) {
  return InkWell(
    onTap: onTap,
    child: Container(
      width: size * .9,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          border: Border.all(color: black.withOpacity(.1))),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
        ),
        child: ListTile(
          contentPadding: EdgeInsets.zero,
          title: Text(title),
          subtitle: Text(value),
        ),
      ),
    ),
  );
}