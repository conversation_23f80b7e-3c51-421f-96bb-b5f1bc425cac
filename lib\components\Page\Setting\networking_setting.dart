import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/Settings/social_items.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';

import '../../../controllers/Pages/pages_details_controller.dart';
import '../../../services/api_services.dart';
import '../../../widgets/button_widget.dart';

class PageNetworkingSetting extends StatelessWidget {
  final String pageID;

  const PageNetworkingSetting({Key? key, required this.pageID})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final pageData = Get.put(PageDetailsController());
    final data = pageData.pageData.value;
    final size = MediaQuery.of(context).size.width;
    final facebook = TextEditingController();
    final twitter = TextEditingController();
    final vk = TextEditingController();
    final linkedin = TextEditingController();
    final instagram = TextEditingController();
    final youtube = TextEditingController();
    facebook.text = data.facebook!;
    twitter.text = data.twitter!;
    vk.text = data.vk!;
    linkedin.text = data.linkedin!;
    instagram.text = data.instgram!;
    youtube.text = data.youtube!;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Networking'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
          vertical: 10,
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Twitter',
                controller: twitter,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Vkontakte',
                controller: vk,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Linkedin',
                controller: linkedin,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'Instagram',
                controller: instagram,
              ),
              sizedBox(10, 0),
              socialItems(
                size: size,
                title: 'YouTube',
                controller: youtube,
              ),
              sizedBox(15, 0),
              CustomButton(
                title: 'Save',
                onTap: () {
                  APISERvices.updatePageSocial(
                    context: context,
                    facebook: facebook.text,
                    twitter: twitter.text,
                    vk: vk.text,
                    instagram: instagram.text,
                    linkedin: linkedin.text,
                    youtube: youtube.text,
                  );
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
