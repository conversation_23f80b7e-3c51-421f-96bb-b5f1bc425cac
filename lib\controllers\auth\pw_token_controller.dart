import 'dart:convert';
import 'package:get/get.dart';
import '../../utils/config.dart';

class TKController extends GetxController {
  var total = {}.obs;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  getData() async {
    try {
      //  استخدام  `String.fromEnvironment`  ل  الحصول على  `authToken`  من  المتغيرات  البيئية
      // final authTokenString = String.fromEnvironment('authToken');
      // final data = jsonDecode(authTokenString); //  فكّ تشفير  JSON
      // total.value = data;
    } catch (e) {
      print("حدث خطأ عند محاولة فك JSON: $e");
    }
    update();
  }
}
