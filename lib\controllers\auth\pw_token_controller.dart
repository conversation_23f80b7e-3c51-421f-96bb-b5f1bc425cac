import 'package:get/get.dart';

class TKController extends GetxController {
  var total = {}.obs;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  getData() async {
    try {
      // إعداد البيانات الافتراضية للتطبيق
      total.value = {
        'sd': [
          {
            'baseUrl': 'https://freedoms-app.com',
            'skt': 'type',
            'sk': 'user_login',
            'ln': 'https://freedoms-app.com/app_api.php?type=user_login&application=phone',
            'wa': 'https://freedoms-app.com'
          }
        ]
      };
    } catch (e) {
      // في حالة حدوث خطأ، استخدم البيانات الافتراضية
      total.value = {
        'sd': [
          {
            'baseUrl': 'https://freedoms-app.com',
            'skt': 'type',
            'sk': 'user_login',
            'ln': 'https://freedoms-app.com/app_api.php?type=user_login&application=phone',
            'wa': 'https://freedoms-app.com'
          }
        ]
      };
    }
    update();
  }
}
