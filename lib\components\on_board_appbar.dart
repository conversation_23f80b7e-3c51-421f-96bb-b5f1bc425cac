import 'package:flutter/material.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
AppBar onBoardAppBar() {
  return AppBar(
    automaticallyImplyLeading: false,
    elevation: 0,
    backgroundColor: background,
    leading: Padding(
      padding: const EdgeInsets.all(15.0),
      child: Image.asset(
        appIcon,
        color: primary,
        width: 30,
      ),
    ),
  );
}