import 'package:flutter/material.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/widgets/Pages/app_bar.dart';
import 'package:get/get.dart';

import '../../../controllers/Pages/pages_details_controller.dart';
import '../../../services/api_services.dart';
import '../../../widgets/button_widget.dart';
import '../../../widgets/form_helper.dart';
import '../../size_box.dart';

class PageInformation extends StatelessWidget {
  final String pageID;

  const PageInformation({Key? key, required this.pageID}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final data = Get.put(PageDetailsController());
    final pageData = data.pageData.value;
    final company = TextEditingController();
    final phone = TextEditingController();
    final location = TextEditingController();
    final website = TextEditingController();
    company.text = pageData.company!;
    phone.text = pageData.phone!;
    location.text = pageData.address!;
    website.text = pageData.website!;

    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Page Information'),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            customFieldHelper(
              controller: company,
              prefixIcon: Icons.work_outline,
              label: 'Company',
            ),
            sizedBox(15, 0),
            customFieldHelper(
              controller: phone,
              prefixIcon: Icons.phone_android,
              label: 'Phone Number',
            ),
            sizedBox(15, 0),
            customFieldHelper(
              controller: website,
              prefixIcon: Icons.web_outlined,
              label: 'Website',
            ),
            sizedBox(15, 0),
            customFieldHelper(
              controller: location,
              prefixIcon: Icons.location_on_rounded,
              label: 'Location',
            ),
            sizedBox(15, 0),
            CustomButton(
              title: 'Save',
              onTap: () {
                APISERvices.updatePageInfo(
                  pageID: pageID,
                  context: context,
                  company: company.text,
                  phone: phone.text,
                  website: website.text,
                  location: location.text
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
