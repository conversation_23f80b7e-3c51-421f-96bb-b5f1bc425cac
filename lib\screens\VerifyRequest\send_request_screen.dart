import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../controllers/UserData/get_user_data_controller.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';
import '../../widgets/button_widget.dart';

class SendRequestScreen extends StatefulWidget {
  final String name;
  final String docPath;
  final String selfiePath;
  final String email;
  final String dateOfBirth;
  final String phnNumber;

  const SendRequestScreen({
    Key? key,
    required this.name,
    required this.docPath,
    required this.selfiePath,
    required this.email,
    required this.dateOfBirth,
    required this.phnNumber,
  }) : super(key: key);

  @override
  State<SendRequestScreen> createState() => _SendRequestScreenState();
}

class _SendRequestScreenState extends State<SendRequestScreen> {
  bool? checked = false;
  bool apiCall = false;

  @override
  Widget build(BuildContext context) {
    final profileController = Get.put(MyDataController());
    final data = profileController.getUserDetails.value;
    String messageToAdmin =
        'Hi I am ${widget.name}. I am interested to get verified badge in my profile on $appName app.\n'
        'My Personal Details is below:\n'
        'Full Name: ${data.name}\nEmail Address: ${widget.email}\nDate Of Birth: ${widget.dateOfBirth}\nPhone Number: ${widget.phnNumber}\n'
        'UserID: ${data.userId}\nUsername: ${data.userName!}\nThank you.';
    final size = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: background,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: background,
        title: Text(
          'Send Documents',
          style: TextStyle(
              color: black.withOpacity(.5),
              fontSize: 17,
              fontWeight: FontWeight.w400),
        ),
        centerTitle: true,
        leading: IconButton(
            onPressed: () {
              Get.back();
            },
            icon: Icon(
              Icons.arrow_back_ios_new,
              color: black.withOpacity(.5),
            )),
      ),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(.1), shape: BoxShape.circle),
              child: Lottie.asset(
                'assets/json/send.json',
                width: size * .4,
              ),
            ),
            sizedBox(20, 0),
            Container(
              width: size * .9,
              decoration: BoxDecoration(
                  color: white.withOpacity(.5),
                  borderRadius: BorderRadius.circular(15),
                  border: Border.all(color: black.withOpacity(.4))),
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 15,
                    vertical: 10,
                  ),
                  child: Column(
                    children: [
                      Text(
                        messageToAdmin,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Checkbox(
                    value: checked,
                    onChanged: (value) {
                      setState(() {
                        checked = !checked!;
                      });
                    }),
                InkWell(
                  onTap: () {
                    setState(() {
                      checked = !checked!;
                    });
                  },
                  child: const Text(
                    'The documents all I provide is true.',
                    style: TextStyle(color: black, fontWeight: FontWeight.w500),
                  ),
                )
              ],
            ),
            sizedBox(10, 0),
            CustomButton(
              bgColor: checked! ? primary : Colors.grey.withOpacity(.5),
              title: 'Send Request',
              onTap: () {
                if (checked!) {
                  Get.snackbar(
                      'Please wait...', 'Your request is being sent to admin.');
                  APISERvices.userVerification(
                    docPath: widget.docPath,
                    selfiePath: widget.selfiePath,
                    name: widget.name,
                    message: messageToAdmin,
                  );
                }
              },
            )
          ],
        ),
      ),
    );
  }
}
