import 'dart:convert';

DataModel dataModelFromJson(String str) => DataModel.fromJson(json.decode(str));

String dataModelToJson(DataModel data) => json.encode(data.toJson());

class DataModel {
  int? apiStatus;
  List<Datum>? data;

  DataModel({
    this.apiStatus,
    this.data,
  });

  factory DataModel.fromJson(Map<String, dynamic> json) => DataModel(
        apiStatus: json["api_status"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "api_status": apiStatus,
      };
}

class Datum {
  String? postId;
  String? postLink;
  String? postText;
  String? reactType;
  int? isBoosted;
  String? postType;
  bool? isGroupPost;
  String? pubAvatar;
  String? pubName;
  String? pubId;
  String? isVerified;
  String? isPro;
  String? groupName;
  String? groupCover;
  String? postDate;
  String? postFeeling;
  String? multiImage;
  List<PhotoMulti>? multiPhotoAlbum;
  String? postFile;
  String? totalComments;
  String? totalShares;
  int? totalLikes;
  SharedInfo? sharedInfo;
  List<GetPostComment>? getPostComments;
  bool? isAuthor;
  String? isComment;
  String? pageID;
  String? groupID;

  Datum({
    this.postId,
    this.postLink,
    this.postText,
    this.reactType,
    this.isBoosted,
    this.postType,
    this.isGroupPost,
    this.pubAvatar,
    this.pubName,
    this.pubId,
    this.pageID,
    this.isPro,
    this.isVerified,
    this.groupName,
    this.groupCover,
    this.postDate,
    this.postFeeling,
    this.multiImage,
    this.multiPhotoAlbum,
    this.postFile,
    this.totalComments,
    this.totalShares,
    this.totalLikes,
    this.sharedInfo,
    this.getPostComments,
    this.isAuthor,
    this.isComment,
    this.groupID,
  });

  void setIsActive(String value) {
    reactType = value;
  }

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        postId: json["post_id"],
        postLink: json['url'],
        postText: json['postText'],
        isBoosted: json['is_post_boosted'],
        reactType: json['reaction']['type'],
        postType: json['postType'],
        isGroupPost: json['group_recipient_exists'],
        pubAvatar: json['publisher']['avatar'],
        pubName: json['publisher']['name'],
        pubId: json['publisher']['user_id'],
        isPro: json['publisher']['is_pro'],
        isVerified: json['publisher']['verified'],
        pageID: json['publisher']['page_id'],
        groupID: json['group_id'],
        groupName: json['group_recipient'] == null
            ? ''
            : json['group_recipient']['group_title'],
        groupCover: json['group_recipient'] == null
            ? ''
            : json['group_recipient']['cover'],
        postDate: json['post_time'],
        postFeeling: json['postFeeling'],
        multiImage: json['multi_image'],
        multiPhotoAlbum: json["photo_multi"] == null
            ? []
            : List<PhotoMulti>.from(
                json["photo_multi"]!.map((x) => PhotoMulti.fromJson(x))),
        postFile: json['postFile'],
        totalComments: json['post_comments'],
        totalShares: json['post_shares'],
        totalLikes: json['reaction']['count'],
        sharedInfo: json["shared_info"] == null
            ? null
            : SharedInfo.fromJson(json["shared_info"]),
        getPostComments: json["get_post_comments"] == null ? [] : List<GetPostComment>.from(json["get_post_comments"]!.map((x) => GetPostComment.fromJson(x))),
        isAuthor: json['admin'],
        isComment: json['comments_status'],
      );
}

class PhotoMulti {
  String? id;
  String? image;
  String? postId;
  String? parentId;
  String? imageOrg;

  PhotoMulti({
    this.id,
    this.image,
    this.postId,
    this.parentId,
    this.imageOrg,
  });

  factory PhotoMulti.fromJson(Map<String, dynamic> json) => PhotoMulti(
        id: json["id"],
        image: json["image"],
        postId: json["post_id"],
        parentId: json["parent_id"],
        imageOrg: json["image_org"],
      );
}

class SharedInfo {
  String? fromName;
  String? fromAvatar;
  String? fromPostTime;
  String? fromIsVerified;
  String? fromIsPro;
  String? fromPostText;

  SharedInfo({
    this.fromAvatar,
    this.fromIsPro,
    this.fromIsVerified,
    this.fromName,
    this.fromPostTime,
    this.fromPostText,
  });

  factory SharedInfo.fromJson(Map<String, dynamic> json) => SharedInfo(
      fromAvatar: json['publisher']['avatar'],
      fromName: json['publisher']['name'],
      fromPostTime: json['post_time'],
      fromIsPro: json['publisher']['is_pro'],
      fromIsVerified: json['publisher']['verified'],
      fromPostText: json['postText']);
}

class GetPostComment {
  String? text;
  String? cPubName;
  String? cPubAvatar;

  GetPostComment({
    this.text,
    this.cPubName,
    this.cPubAvatar
  });

  factory GetPostComment.fromJson(Map<String, dynamic> json) => GetPostComment(
    text: json["text"],
    cPubName: json['publisher']['name'],
    cPubAvatar: json['publisher']['avatar']
  );
}
