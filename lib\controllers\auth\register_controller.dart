import 'dart:convert';
import 'package:flutter_wowonder/components/response_error_toast.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/auth/pw_token_controller.dart';
import 'package:get/get.dart';
import '../../components/navigation.dart';
import '../../utils/config.dart';
import 'package:http/http.dart' as http;

class SignUpController extends GetxController {
  var isLoading = false.obs;
  static TKController controller = Get.put(TKController());

  Future<void> userRegister(
      String username, String email, String password, String confirmPassword) async {
    isLoading.value = true;
    final response =
        await http.post(Uri.parse(controller.total['sd'][0]['ca']), body: {
          controller.total['sd'][0]['skt'] : controller.total['sd'][0]['sk'],
          'username' : username,
          'email' : email,
          'password' : password,
          'confirm_password' : confirmPassword
        });
    final jsonData = jsonDecode(response.body);
    if (response.statusCode == 200) {
      if (jsonData['api_status'] != 200) {
        responseError(jsonData);
        isLoading.value = false;
      } else {
        Get.snackbar(succReg, dataStorated,
            snackPosition: SnackPosition.BOTTOM);
        sizedBox(20, 0);
        navigate('login');
        isLoading.value = false;
      }
    } else {
      isLoading.value = false;
    }
  }
}
