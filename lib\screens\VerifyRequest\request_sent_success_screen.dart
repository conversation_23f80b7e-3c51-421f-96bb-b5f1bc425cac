import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:lottie/lottie.dart';
import '../../components/size_box.dart';
import '../../utils/colors.dart';
import '../../widgets/button_widget.dart';

class RequestSentSuccess extends StatelessWidget {
  const RequestSentSuccess({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset('assets/json/success.json'),
            const Text(
              'Success',
              style: TextStyle(
                  color: primary, fontWeight: FontWeight.w600, fontSize: 35),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                'Your request has been successfully sent to admin, please wait for feedback.',
                textAlign: TextAlign.center,
              ),
            ),
            sizedBox(20, 0),
            CustomButton(
              title: 'Sounds Good!',
              onTap: () {
                navigate('more');
              },
            ),
          ],
        ),
      ),
    );
  }
}
