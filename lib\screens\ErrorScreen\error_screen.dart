import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_wowonder/widgets/button_widget.dart';
import 'package:lottie/lottie.dart';

import '../../components/size_box.dart';
import '../../utils/colors.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: Colors.red,
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Lottie.asset('assets/json/error.json', width: size * 4),
            sizedBox(10, 0),
            const Text('Access Blocked!!!',
            style: TextStyle(
              color: white,
              fontSize: 35,
              fontWeight: FontWeight.bold
            ),),
            const Text('Please provide valid license key in config.dart file.',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: white,
            ),),
            Text('Location => Utils >> Config.dart',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: white.withOpacity(.5)
              ),),
            sizedBox(20, 0),
            CustomButton(title: 'Ok', onTap: (){
              SystemChannels.platform.invokeMethod('SystemNavigator.pop');
            },),
          ],
        ),
      ),
    );
  }
}
