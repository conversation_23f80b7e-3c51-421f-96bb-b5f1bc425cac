import 'package:flutter/material.dart';
import '../utils/colors.dart';

PreferredSizeWidget insidePageAppBar({
  required double size,
  required String title,
  required BuildContext context,
  List<PopupMenuEntry<Object?>>? menuItem,
  void Function(Object?)? onSelected,
}) {
  return AppBar(
    automaticallyImplyLeading: false,
    backgroundColor: Colors.transparent,
    elevation: 0,
    title: Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            Navigator.pop(context);
          },
          child: Container(
              height: 40,
              width: 40,
              decoration: const BoxDecoration(
                color: primary,
                shape: BoxShape.circle,
              ),
              child: const Padding(
                padding: EdgeInsets.all(8.0),
                child: Icon(
                  Icons.arrow_back_ios_new,
                  color: white,
                ),
              )),
        ),
        Text(title),
        Container(
          height: 40,
          width: 40,
          decoration: const BoxDecoration(
            color: primary,
            shape: BoxShape.circle,
          ),
          child: PopupMenuButton(
            color: white,
            itemBuilder: (_) => menuItem!,
            onSelected: onSelected,
          ),
        ),
      ],
    ),
  );
}
