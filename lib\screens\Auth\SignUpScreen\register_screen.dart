import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:flutter_wowonder/utils/config.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import '../../../controllers/auth/register_controller.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final passController = TextEditingController();
  final confirmPassController = TextEditingController();
  final usernameController = TextEditingController();
  final emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool showPassword = true;
  bool isApiCallProcess = false;
  final _controller = SignUpController();

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size.width;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: false,
      backgroundColor: background,
      appBar: AppBar(
        centerTitle: true,
        backgroundColor: background,
        elevation: 0,
        leading: IconButton(
            onPressed: () {
              Navigator.pop(context);
            },
            icon: const Icon(
              Icons.arrow_back_ios_outlined,
              size: 20,
              color: primary,
            )),
        title: Text(
          register,
          style: const TextStyle(
            color: primary,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
      body: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Container(
              width: size * .9,
              decoration: BoxDecoration(
                color: white,
                boxShadow: [
                  BoxShadow(
                    color: black.withOpacity(0.1),
                    blurRadius: 5,
                  )
                ],
                borderRadius: BorderRadius.circular(15),
              ),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Column(
                  children: [
                    Column(
                      children: [
                        Container(
                          height: 100,
                          width: 100,
                          decoration: BoxDecoration(
                            border: Border.all(color: primary, width: 2),
                            color: background,
                            shape: BoxShape.circle,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: Center(
                              child: Lottie.asset(lockJson),
                            ),
                          ),
                        ),
                        sizedBox(20, 0),
                        Text(
                          register,
                          style: const TextStyle(
                            color: black,
                            fontWeight: FontWeight.w600,
                            fontSize: 25,
                          ),
                        ),
                        Text(
                          fillData,
                          style: TextStyle(
                            color: black.withOpacity(0.5),
                            fontWeight: FontWeight.w300,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    sizedBox(20, 0),
                    Form(
                        key: _formKey,
                        child: Column(
                          children: [
                            TextFormField(
                              controller: usernameController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return usernameError;
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(
                                    Icons.alternate_email_outlined),
                                label: Text(username),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(15, 0),
                            TextFormField(
                              controller: emailController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return emailError;
                                }
                                return null;
                              },
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(Icons.email_outlined),
                                label: Text(email),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(15, 0),
                            TextFormField(
                              controller: passController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return passError;
                                } else if (value.length < 6) {
                                  return strongPass;
                                }
                                return null;
                              },
                              obscuringCharacter: secureText,
                              obscureText: showPassword,
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(Icons.lock_open),
                                suffixIcon: InkWell(
                                  onTap: () {
                                    showPassword = !showPassword;
                                    setState(() {});
                                  },
                                  child: showPassword
                                      ? const Icon(Icons.visibility)
                                      : const Icon(Icons.visibility_off),
                                ),
                                label: Text(password),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(15, 0),
                            TextFormField(
                              controller: confirmPassController,
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return passError;
                                } else if (value.length < 6) {
                                  return strongPass;
                                }
                                return null;
                              },
                              obscuringCharacter: secureText,
                              obscureText: showPassword,
                              decoration: InputDecoration(
                                prefixIconColor: black.withOpacity(0.5),
                                suffixIconColor: black.withOpacity(0.5),
                                focusColor: Colors.red,
                                prefixIcon: const Icon(Icons.lock_open),
                                suffixIcon: InkWell(
                                  onTap: () {
                                    showPassword = !showPassword;
                                    setState(() {});
                                  },
                                  child: showPassword
                                      ? const Icon(Icons.visibility)
                                      : const Icon(Icons.visibility_off),
                                ),
                                label: Text(confirmPass),
                                enabledBorder: OutlineInputBorder(
                                  borderSide: BorderSide(
                                    color: black.withOpacity(0.1),
                                    width: 1.5,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                border: OutlineInputBorder(
                                  borderSide: const BorderSide(
                                    color: black,
                                    width: 2,
                                  ),
                                  borderRadius: BorderRadius.circular(15),
                                ),
                              ),
                            ),
                            sizedBox(20, 0),
                          ],
                        )),
                    Obx(() {
                      if (_controller.isLoading.value) {
                        return const CircularProgressIndicator();
                      } else {
                        return InkWell(
                          onTap: () {
                            _controller.userRegister(
                              usernameController.text,
                              emailController.text,
                              passController.text,
                              confirmPassController.text
                            );
                          },
                          child: Container(
                            height: 55,
                            width: size * .9,
                            decoration: BoxDecoration(
                              color: primary,
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Center(
                              child: Text(
                                register,
                                style: const TextStyle(
                                  letterSpacing: 2,
                                  color: white,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                        );
                      }
                    }),
                    sizedBox(20, 0),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(alreadyAcc),
                        InkWell(
                          onTap: () {
                            navigate('login');
                          },
                          child: Text(
                            loginHere,
                            style: const TextStyle(
                              color: primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            sizedBox(50, 0),
          ],
        ),
      ),
    );
  }

  bool validateAndSave() {
    final form = _formKey.currentState;
    if (form!.validate()) {
      form.save();
      return true;
    } else {
      return false;
    }
  }
}
