import 'package:flutter/material.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../utils/colors.dart';

Widget multiImage(data, size, controller) {
  return Stack(
    alignment: Alignment.bottomCenter,
    children: [
      Container(
        height: 250,
        width: size * .9,
        decoration: BoxDecoration(
          color: Colors.grey,
          borderRadius: BorderRadius.circular(20),
        ),
        child: PageView.builder(
            controller: controller,
            itemCount: data.multiPhotoAlbum!.length,
            itemBuilder: (_, index) {
              return ClipRRect(
                borderRadius: BorderRadius.circular(
                  20,
                ),
                child: Image.network(
                  data.multiPhotoAlbum![index].image!,
                  fit: BoxFit.cover,
                ),
              );
            }),
      ),
      Positioned(
        bottom: 10,
        child: Container(
            alignment: const Alignment(0, 0.75),
            child: SmoothPageIndicator(
              effect: WormEffect(
                paintStyle: PaintingStyle.stroke,
                type: WormType.thin,
                activeDotColor: primary,
                dotColor: primary.withOpacity(0.5),
              ),
              controller: controller,
              count: data.multiPhotoAlbum!.length,
            )),
      )
    ],
  );
}
