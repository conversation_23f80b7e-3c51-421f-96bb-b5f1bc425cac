import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/menu_items.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/profile_screen.dart';
import 'package:flutter_wowonder/services/shared_services.dart';
import 'package:flutter_wowonder/utils/colors.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../components/navigation.dart';
import '../../components/push_navigator.dart';
import '../../controllers/bottom_bar_controller.dart';
import '../../utils/config.dart';

class MoreOptionScreen extends StatefulWidget {
  const MoreOptionScreen({Key? key}) : super(key: key);

  @override
  State<MoreOptionScreen> createState() => _MoreOptionScreenState();
}

class _MoreOptionScreenState extends State<MoreOptionScreen> {
  final bottomController = Get.put(BottomBarController());
  String myID = '';

  Future<void> _launchUrl(String url) async {
    if (!await launchUrl(Uri.parse(url))) {
      throw Exception('Could not launch $url');
    }
  }

  myIDData () async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    setState(() {
      myID = id!;
    });
  }
  @override
  void initState() {
    myIDData();
    super.initState();
  }
  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: background,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 15),
          child: Container(
            decoration: BoxDecoration(
                color: white, borderRadius: BorderRadius.circular(20)),
            child: Padding(
              padding: const EdgeInsets.all(15.0),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LongMenuItems(
                      onTap: () {
                        myIDData();
                        myIDData();
                        pageRoute(
                            context,
                            MyProfileScreen(
                              userID: myID,
                            ));
                      },
                      icon: 'assets/svg/profile.svg',
                      iconSize: 26,
                      title: 'My Profile',
                    ),
                    sizedBox(20, 0),
                    Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            MenuOptionItem(
                              onTap: (){
                                bottomController.changeTabIndex(1);
                              },
                              icon: 'assets/svg/play_fill.svg',
                              title: 'Watch Video',
                            ),
                            sizedBox(0, 10),
                            MenuOptionItem(
                              onTap: () {
                                navigate('getVerified');
                              },
                              icon: 'assets/svg/verify.svg',
                              title: 'Verification',
                            ),
                          ],
                        ),
                        sizedBox(20, 0),
                        LongMenuItems(
                          onTap: (){
                            navigate('saved');
                          },
                          icon: 'assets/svg/bookmark.svg',
                          title: 'Saved',
                          iconSize: 16,
                        ),
                        sizedBox(20, 0),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            MenuOptionItem(
                              onTap: () {
                                navigate('pageList');
                              },
                              icon: 'assets/svg/pages.svg',
                              iconSize: 26,
                              title: 'Pages',
                            ),

                            sizedBox(0, 10),
                            MenuOptionItem(
                              onTap: (){
                                navigate('groupList');
                              },
                              icon: 'assets/svg/groups.svg',
                              iconSize: 26,
                              title: 'Groups',
                            ),
                          ],
                        ),
                      ],
                    ),
                    sizedBox(20, 0),
                    LongMenuItems(
                      onTap: (){
                        _launchUrl(helpSupportUrl);
                      },
                      icon: 'assets/svg/support.svg',
                      title: 'Help & Support',
                    ),
                    sizedBox(20, 0),
                    LongMenuItems(
                      onTap: (){
                        _launchUrl(privacyPolicyUrl);
                      },
                      iconSize: 22,
                      icon: 'assets/svg/privacy.svg',
                      title: 'Privacy Policy',
                    ),
                    sizedBox(20, 0),
                    LongMenuItems(
                      onTap: () {
                        navigate('settings');
                      },
                      icon: 'assets/svg/settings.svg',
                      title: 'Settings',
                    ),
                    sizedBox(20, 0),
                    LongMenuItems(
                      icon: 'assets/svg/logout.svg',
                      title: 'LogOut',
                      onTap: () {
                        SharedService.logOut();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
