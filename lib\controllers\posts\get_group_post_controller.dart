import 'package:flutter_wowonder/models/PostModels/get_group_post_model.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';

class GetGroupPostController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  RxList<GroupDatum> dataList = <GroupDatum>[].obs;

  getData({required String groupID}) async {
    isLoading.value = true;
    var apiData = await apiServices.getGroupPost(
        groupID: groupID
    );
    dataList.value = apiData!.data!;
    isLoading.value = false;
    update();
  }

  Future<void> onRefresh() async {
    isLoading.value = true;
    await Future.delayed(const Duration(seconds: 3));
    var list = await APISERvices().getGroupPost();
    dataList.clear();
    dataList.addAll(list!.data!);
    isLoading.value = false;
    update();
  }

  statusChange(String id, String status, GroupDatum getCountries, int index,
      {String? ccc}) async {
    await apiServices.postReaction(postID: id, action: status, count: ccc);

    getCountries.setIsActive(ccc.toString());
    dataList[index] = getCountries;
    update();
  }
}
