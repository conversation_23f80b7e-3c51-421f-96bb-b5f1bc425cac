import 'package:flutter/material.dart';

import '../../utils/colors.dart';
import 'package:get/get.dart';

PreferredSizeWidget modernAppBar({
  required String title,
  PreferredSizeWidget? bottom,
  Color? bgColor,
  void Function()? onTap,
}) {
  return AppBar(
    bottom: bottom,
    elevation: 0,
    backgroundColor: bgColor ?? background,
    title: Text(
      title,
      style: TextStyle(
          color: black.withOpacity(.5),
          fontSize: 17,
          fontWeight: FontWeight.w400),
    ),
    centerTitle: true,
    leading: IconButton(
        onPressed: onTap ?? () {
          Get.back();
        },
        icon: Icon(
          Icons.arrow_back_ios_new,
          color: black.withOpacity(.5),
        )),
  );
}
