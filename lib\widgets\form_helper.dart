import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/colors.dart';

Widget customFieldHelper({
  TextEditingController? controller,
  String? Function(String?)? validator,
  Color? prefixColor,
  Color? suffixColor,
  String? label,
  IconData? prefixIcon,
  List<TextInputFormatter>? formatter,
  TextInputType? type,
  String? hintText,
  bool? autoFocus,
}) {
  return TextFormField(
    autofocus: autoFocus ?? false,
    controller: controller,
    validator: validator,
    keyboardType: type,
    inputFormatters: formatter,
    decoration: InputDecoration(
      hintText: hintText,
      prefixIconColor: prefixColor ?? black.withOpacity(0.5),
      suffixIconColor: suffixColor ?? black.withOpacity(0.5),
      focusColor: Colors.red,
      prefixIcon: Icon(prefixIcon),
      label: Text(label!),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: black.withOpacity(0.1),
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      border: OutlineInputBorder(
        borderSide: const BorderSide(
          color: black,
          width: 2,
        ),
        borderRadius: BorderRadius.circular(15),
      ),
    ),
  );
}
