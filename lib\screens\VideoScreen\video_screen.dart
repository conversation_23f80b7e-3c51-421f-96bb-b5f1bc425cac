import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/posts/get_vide_post_controller.dart';
import 'package:refresh_loadmore/refresh_loadmore.dart';
import '../../utils/colors.dart';
import '../../widgets/video_post_data.dart';
import 'package:get/get.dart';

class VideoScreen extends StatelessWidget {
  const VideoScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(GetVideoPostController());
    return RefreshLoadmore(
      onRefresh: controller.onRefresh,
      onLoadmore: controller.onLoading,
      isLastPage: false,
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children:  [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10),
              child: Text(
                'Videos',
                style: TextStyle(
                    color: black.withOpacity(0.5),
                    fontSize: 22,
                    fontWeight: FontWeight.w600),
              ),
            ),
            const VideoPostData()
          ],
        ),
      ),
    );
  }
}
