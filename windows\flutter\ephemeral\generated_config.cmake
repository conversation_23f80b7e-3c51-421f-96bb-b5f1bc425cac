# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\tools\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\Hold\\Akram K\\source_code" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\tools\\flutter"
  "PROJECT_DIR=D:\\Hold\\Akram K\\source_code"
  "FLUTTER_ROOT=C:\\tools\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\Hold\\Akram K\\source_code\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\Hold\\Akram K\\source_code"
  "FLUTTER_TARGET=D:\\Hold\\Akram K\\source_code\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9lZGQ4NTQ2MTE2NDU3YmRmMWM1YmRmYjEzZWNiOTQ2M2QyYmI1ZWQ0Lw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\Hold\\Akram K\\source_code\\.dart_tool\\package_config.json"
)
