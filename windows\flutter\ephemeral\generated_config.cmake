# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\tools\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+0" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\tools\\flutter"
  "PROJECT_DIR=E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code"
  "FLUTTER_ROOT=C:\\tools\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code"
  "FLUTTER_TARGET=E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\AmrDev-All Work\\OLD\\Hold\\OLD----\\Akram K\\old\\source_code\\.dart_tool\\package_config.json"
)
