import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/screens/GroupsScreen/group_details_screen.dart';
import 'package:get/get.dart';

import '../../components/Shimmer/shimmer_effect.dart';
import '../../components/size_box.dart';
import '../../controllers/Search/search_controller.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';

class GroupSearchResults extends GetView<SearchResultsController> {
  const GroupSearchResults({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Obx(() {
      if (controller.isLoading.value) {
        return searchResultShimmer(size);
      } else {
        if (controller.searchData.value.groups!.isEmpty) {
          return Center(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  noData,
                  width: size.width * .5,
                ),
                const Text(
                  'No data!',
                  style: TextStyle(color: primary, fontSize: 30),
                ),
                const Text('There are no data available!')
              ],
            ),
          );
        } else {
          return Padding(
            padding: const EdgeInsets.all(10.0),
            child: ListView.builder(
                shrinkWrap: true,
                primary: false,
                itemCount: controller.searchData.value.groups!.length,
                itemBuilder: (_, index) {
                  final data = controller.searchData.value.groups![index];
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 10.0),
                    child: Container(
                      width: size.width * .9,
                      decoration: BoxDecoration(
                        boxShadow: [
                          BoxShadow(
                              color: black.withOpacity(.100), blurRadius: 1)
                        ],
                        color: white,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15.0,
                          vertical: 8,
                        ),
                        child: Row(
                          children: [
                            Container(
                              height: 60,
                              width: 60,
                              decoration: BoxDecoration(
                                color: primary,
                                borderRadius: BorderRadius.circular(15),
                                image: data.cover! ==
                                        '$siteUrl/upload/photos/d-cover.jpg  '
                                    ? DecorationImage(
                                        image: NetworkImage(
                                            '$siteUrl/upload/photos/d-cover.jpg'),
                                        fit: BoxFit.cover,
                                      )
                                    : DecorationImage(
                                        image: NetworkImage(data.cover!),
                                        fit: BoxFit.cover),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.all(2.0),
                                    child: CircleAvatar(
                                      radius: 15,
                                      backgroundImage: data.avatar! ==
                                              "$siteUrl/upload/photos/d-group.jpg "
                                          ? NetworkImage(
                                              '$siteUrl/upload/photos/d-group.jpg')
                                          : NetworkImage(data.avatar!),
                                    ),
                                  )
                                ],
                              ),
                            ),
                            sizedBox(0, 10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        pageRoute(
                                            context,
                                            GroupDetailsScreen(
                                                groupID: data.groupId!));
                                      },
                                      child: Text(
                                        data.name!,
                                        style: const TextStyle(
                                            color: black,
                                            fontSize: 19,
                                            fontWeight: FontWeight.w400),
                                      ),
                                    ),
                                  ],
                                ),
                                sizedBox(3, 0),
                                Row(
                                  children: [
                                    data.privacy == '1'
                                        ? const Text('Public •')
                                        : const Text('Private •'),
                                    sizedBox(0, 5),
                                    Text('${data.membersCount} members'),
                                  ],
                                ),
                                sizedBox(3, 0),
                                Text(
                                  data.category!,
                                  style:
                                      TextStyle(color: black.withOpacity(.5)),
                                ),
                                sizedBox(3, 0),
                                InkWell(
                                  onTap: () {
                                    pageRoute(
                                        context,
                                        GroupDetailsScreen(
                                            groupID: data.groupId!));
                                  },
                                  child: Container(
                                    width: size.width * .650,
                                    decoration: BoxDecoration(
                                        borderRadius:
                                        BorderRadius.circular(10),
                                        color: primary),
                                    child: const Padding(
                                      padding: EdgeInsets.symmetric(
                                          vertical: 10.0),
                                      child: Center(
                                        child: Text(
                                          'Visit Group',
                                          style: TextStyle(color: white),
                                        ),
                                      ),
                                    ),
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          );
        }
      }
    });
  }
}
