import 'package:flutter/material.dart';

import '../size_box.dart';

Widget postFeeling(data) {
  return Container(
    decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(5)),
    child: Padding(
      padding: const EdgeInsets.all(5.0),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Is feeling',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
            sizedBox(0, 5),
            Text(data.postFeeling!, style: const TextStyle(color: Colors.grey))
          ],
        ),
      ),
    ),
  );
}
