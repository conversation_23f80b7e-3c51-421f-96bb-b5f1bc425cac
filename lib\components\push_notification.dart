// Future<void> sendNotification(String token, title, body) async {
//   const postUrl = 'https://fcm.googleapis.com/fcm/send';
//   final data = {
//     "notification": {
//       "title": "$title",
//       "body": "$body",
//       "click_action": "FLUTTER_NOTIFICATION_CLICK"
//     },
//     "to": token
//   };
//
//   final headers = {
//     'content-type': 'application/json',
//     'Authorization': 'key=$serverKey'
//   };
//
//   final response = await http.post(Uri.parse(postUrl),
//       headers: headers, body: json.encode(data));
//
//   if (response.statusCode == 200) {
//     print("send");
//   } else {
//     // notification sending failed
//   }
// }