import 'package:flutter/material.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/services/api_services.dart';
import 'package:flutter_wowonder/utils/colors.dart';

import '../../utils/config.dart';
import '../../widgets/Pages/app_bar.dart';
import '../../widgets/button_widget.dart';
import '../../widgets/form_helper.dart';

class SecuritySetting extends StatefulWidget {
  const SecuritySetting({Key? key}) : super(key: key);

  @override
  State<SecuritySetting> createState() => _SecuritySettingState();
}

class _SecuritySettingState extends State<SecuritySetting> {
  final formKey = GlobalKey<FormState>();
  final currentPassword = TextEditingController();
  final newPassword = TextEditingController();
  final confirmNewPassword = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: background,
      appBar: modernAppBar(title: 'Security'),
      body: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 15.0,
          vertical: 10,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Change Password',
              style: TextStyle(fontSize: 17, fontWeight: FontWeight.bold),
            ),
            Form(
                key: formKey,
                child: Column(
                  children: [
                    sizedBox(15, 0),
                    customFieldHelper(
                        controller: currentPassword,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return '❌ Please type your password ❌';
                          }
                          return null;
                        },
                        label: 'Current Password',
                        prefixIcon: Icons.password_outlined),
                    sizedBox(15, 0),
                    customFieldHelper(
                        controller: newPassword,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return passError;
                          }
                          return null;
                        },
                        label: 'New Password',
                        prefixIcon: Icons.password_outlined),
                    sizedBox(15, 0),
                    customFieldHelper(
                        controller: confirmNewPassword,
                        validator: (value) {
                          if (value!.isEmpty) {
                            return passError;
                          }
                          return null;
                        },
                        label: 'Confirm New Password',
                        prefixIcon: Icons.password_outlined),
                  ],
                )),
            sizedBox(15, 0),
            CustomButton(
              title: 'Save Changes',
              onTap: () {
                if (formKey.currentState!.validate()) {
                  APISERvices.updatePassword(
                    currentPassword: currentPassword.text,
                    newPassword: newPassword.text, context: context,
                  );
                }
              },
            )
          ],
        ),
      ),
    );
  }
}
