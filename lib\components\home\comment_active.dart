// ignore_for_file: prefer_typing_uninitialized_variables
import 'package:flutter/material.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_data_controller.dart';
import 'package:get/get.dart';
import '../../screens/CommentScreen/comments_screen.dart';
import '../../utils/colors.dart';
import '../Shimmer/shimmer_effect.dart';

class CommentActive extends GetView<MyDataController> {
  final data;

  const CommentActive(this.data, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Get.put(MyDataController());
    final size = MediaQuery.of(context).size.width;
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => PostCommentScreen(postID: data.postId!),
          ),
        );
      },
      child: Container(
        width: size * .9,
        decoration: BoxDecoration(
            color: primary.withOpacity(.1),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: black.withOpacity(.1), width: .5)),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5.0),
          child: Center(
            child: Row(
              children: [
                Obx(() {
                  if (controller.isLoading.value) {
                    return homeProfileShimmer();
                  } else {
                    final data = controller.getUserDetails.value;
                    return data.avatar == null
                        ? homeProfileShimmer()
                        : Container(
                            height: 35,
                            width: 35,
                            decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: DecorationImage(
                                    image: NetworkImage(data.avatar!),
                                    fit: BoxFit.cover)),
                          );
                  }
                }),
                Expanded(
                  child: Center(
                    child: Text(
                      'Write a comment...',
                      style: TextStyle(color: black.withOpacity(.3)),
                    ),
                  ),
                ),
                Icon(
                  Icons.send,
                  color: black.withOpacity(.3),
                  size: 20,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
