import 'package:flutter_wowonder/services/api_services.dart';
import 'package:get/get.dart';
import '../../models/Groups/get_groups_model.dart';

class GetMyGroupsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var myGroupData = GetGroupsModel().data.obs;

  getData({required String type}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getGroups(type: type);
    isLoading.value = false;
    myGroupData.value = apiData!.data!;
    update();
  }
}

class GetJoinedGroupsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var joinedGroupsData = GetGroupsModel().data.obs;

  getData({required String type, String? userID}) async {
    isLoading.value = true;
    var apiData = await APISERvices.getGroups(type: type);
    isLoading.value = false;
    joinedGroupsData.value = apiData!.data!;
    update();
  }
}

class SuggestedGroupsController extends GetxController {
  var isLoading = false.obs;
  APISERvices apiServices = APISERvices();
  var recommendedData = GetGroupsModel().data.obs;

  getData({required String type}) async {
    isLoading.value = true;
    var apiData = await APISERvices.groupsRecommerded(type: type);
    isLoading.value = false;
    recommendedData.value = apiData!.data!;
    update();
  }
}

