// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/push_navigator.dart';
import 'package:flutter_wowonder/controllers/Pages/pages_details_controller.dart';
import 'package:flutter_wowonder/screens/SettingsScreen/page_setting_screen.dart';
import 'package:get/get.dart';
import '../../utils/colors.dart';
import '../size_box.dart';

Widget pageButton(
  PageDetailsController controller,
  String pageID,
  BuildContext context,
    double size,
) {
  return Obx(() => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: controller.pageData.value.isPageOnwer!
            ? InkWell(
              onTap: () {
                pageRoute(context,
                    PageSetting(pageID: controller.pageData.value.pageId!));
              },
              child: Container(
                width: size * .9,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: primary,
                    )),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Center(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SvgPicture.asset(
                          'assets/svg/edit.svg',
                          width: 15,
                          color: primary,
                        ),
                        sizedBox(0, 5),
                        const Text(
                          'Edit Page',
                          style: TextStyle(color: primary),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
            : Row(
                children: [
                  Obx(
                    () => Expanded(
                      child: InkWell(
                        onTap: () {
                          if (controller.pageData.value.isLiked == true) {
                            controller.statusChange(
                                pageID, false, controller.pageData.value);
                          } else {
                            controller.statusChange(
                                pageID, true, controller.pageData.value);
                          }
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: primary,
                            borderRadius: BorderRadius.circular(15),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Center(
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    'assets/svg/like.svg',
                                    width: 15,
                                    color: white,
                                  ),
                                  sizedBox(0, 5),
                                  Text(
                                    controller.pageData.value.isLiked == true
                                        ? 'Liked'
                                        : 'Like',
                                    style: const TextStyle(color: white),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  sizedBox(0, 5),
                  Expanded(
                    child: InkWell(
                      onTap: () {
                        Get.defaultDialog(
                          title: 'Not Available',
                          textCancel: 'Ok',
                          cancelTextColor: primary,
                          content: const Text(
                              'Messaging future is not available right now.'),
                        );
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: primary,
                            )),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Center(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  'assets/svg/message.svg',
                                  width: 15,
                                  color: primary,
                                ),
                                sizedBox(0, 5),
                                const Text(
                                  'Message',
                                  style: TextStyle(color: primary),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
      ));
}
