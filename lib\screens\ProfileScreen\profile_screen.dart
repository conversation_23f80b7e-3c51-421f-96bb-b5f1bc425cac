// ignore_for_file: deprecated_member_use, use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_wowonder/components/Shimmer/shimmer_effect.dart';
import 'package:flutter_wowonder/components/navigation.dart';
import 'package:flutter_wowonder/components/size_box.dart';
import 'package:flutter_wowonder/controllers/UserData/get_user_followers_controller.dart';
import 'package:flutter_wowonder/controllers/posts/get_user_post_controller.dart';
import 'package:flutter_wowonder/screens/ProfileScreen/profile_details_screen.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../components/profile/followers_section.dart';
import '../../components/profile/null_condition.dart';
import '../../controllers/UserData/get_user_data_controller.dart';
import '../../services/api_services.dart';
import '../../utils/colors.dart';
import '../../utils/config.dart';
import '../../widgets/inside_page_appbar.dart';
import '../../widgets/user_post_data.dart';

class MyProfileScreen extends StatefulWidget {
  final String userID;

  const MyProfileScreen({Key? key, required this.userID}) : super(key: key);

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  final controller = Get.put(UserDataController());
  File? images;
  File? cover;
  String myID = '';
  final reportTextController = TextEditingController();
  final formKey = GlobalKey<FormState>();

  void idFinder() async {
    final pref = await SharedPreferences.getInstance();
    final id = pref.getString('id');
    setState(() {
      myID = id!;
    });
  }

  void uploadProfile() async {
    Navigator.pop(context);
    try {
      final image = await ImagePicker().pickImage(
        source: ImageSource.gallery,
      );
      if (image == null) return;
      File? img = File(image.path);
      img = await profileCrop(imageFile: img);
      setState(() {
        images = img;
      });
    } on PlatformException {
      return null;
    }
  }

  Future profileCrop({required File imageFile}) async {
    CroppedFile? croppedImage =
        await ImageCropper().cropImage(sourcePath: imageFile.path);
    if (croppedImage == null) return null;
    APISERvices.updateAvatar(filePath: croppedImage.path, context: context)
        .then((value) {
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile Picture Uploading...')));
      controller.refreshData(userID: widget.userID);
    });
  }

  void uploadCover() async {
    Navigator.pop(context);
    try {
      final image = await ImagePicker().pickImage(
        source: ImageSource.gallery,
      );
      if (image == null) return;
      File? img = File(image.path);
      img = await coverCrop(imageFile: img);
      setState(() {
        cover = img;
      });
    } on PlatformException {
      return null;
    }
  }

  Future coverCrop({required File imageFile}) async {
    CroppedFile? croppedImage =
        await ImageCropper().cropImage(sourcePath: imageFile.path);
    if (croppedImage == null) return null;
    APISERvices.updateAvatar(filePath: croppedImage.path, context: context)
        .then((value) {
      ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Cover Picture Uploading...')));
      controller.refreshData(userID: widget.userID);
    });
  }

  @override
  void initState() {
    idFinder();
    controller.getData(userID: widget.userID);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Get.lazyPut(() => GetUserFollowersController());
    Get.put(GetUserPostController());
    final size = MediaQuery.of(context).size.width;
    final shimmerSize = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: background,
      extendBodyBehindAppBar: true,
      appBar: insidePageAppBar(
        size: size,
        title: '',
        onSelected: (value) {
          switch (value) {
            case 0:
              Get.defaultDialog(
                  title: 'Report',
                  content: Form(
                    key: formKey,
                    child: Container(
                      width: size * .9,
                      decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(.1),
                          borderRadius: BorderRadius.circular(20)),
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 15.0),
                        child: Center(
                          child: TextFormField(
                            validator: (value) {
                              if (value!.isEmpty) {
                                return 'Please write something wrong about this page';
                              }
                              return null;
                            },
                            keyboardType: TextInputType.multiline,
                            minLines: 2,
                            maxLines: 5,
                            controller: reportTextController,
                            decoration: const InputDecoration(
                                border: InputBorder.none,
                                hintText: 'What\'s wrong with this user?'),
                          ),
                        ),
                      ),
                    ),
                  ),
                  textCancel: 'Cancel',
                  textConfirm: 'Send Report',
                  cancelTextColor: primary,
                  confirmTextColor: white,
                  onCancel: () {
                    reportTextController.clear();
                  },
                  onConfirm: () {
                    if (formKey.currentState!.validate()) {
                      APISERvices.reportUser(
                        userID: widget.userID,
                        text: reportTextController.text.toString(),
                      );
                      reportTextController.clear();
                      Navigator.pop(context);
                      Get.snackbar(
                        'Report Sent',
                        'Your report has been sent to our team successfully.',
                      );
                    } else {
                      return;
                    }
                  });
              break;
            case 1:
              Clipboard.setData(
                  ClipboardData(text: controller.getUserDetails.value.url!));
              Get.snackbar('Copied', 'Page link copied successfully!');
          }
        },
        menuItem: [
          const PopupMenuItem(value: 0, child: Text('Report User')),
          const PopupMenuItem(value: 1, child: Text('Copy link')),
        ],
        context: context,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: profileShimmer(shimmerSize),
          );
        } else {
          final data = controller.getUserDetails.value;
          return nullCondition(controller)
              ? profileShimmer(shimmerSize)
              : RefreshIndicator(
                  onRefresh: () {
                    return controller.refreshData(userID: widget.userID);
                  },
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Stack(
                          clipBehavior: Clip.none,
                          alignment: Alignment.bottomCenter,
                          children: [
                            InkWell(
                              onTap: () {
                                myID == data.userId
                                    ? Get.bottomSheet(Container(
                                        height: shimmerSize.height * .18,
                                        width: shimmerSize.width,
                                        decoration: const BoxDecoration(
                                          color: white,
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15, vertical: 10.0),
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            children: [
                                              ListTile(
                                                onTap: () {
                                                  Navigator.pop(context);
                                                  showDialog(
                                                      context: context,
                                                      builder:
                                                          (_) => AlertDialog(
                                                                insetPadding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                contentPadding:
                                                                    EdgeInsets
                                                                        .zero,
                                                                clipBehavior: Clip
                                                                    .antiAliasWithSaveLayer,
                                                                shape: const RoundedRectangleBorder(
                                                                    borderRadius:
                                                                        BorderRadius.all(
                                                                            Radius.circular(10.0))),
                                                                content: Builder(
                                                                    builder:
                                                                        (context) {
                                                                  return Image
                                                                      .network(
                                                                    data.cover!,
                                                                    fit: BoxFit
                                                                        .cover,
                                                                  );
                                                                }),
                                                              ));
                                                },
                                                leading: const Icon(
                                                    Icons.remove_red_eye),
                                                title: const Text(
                                                    'See Cover Picture'),
                                              ),
                                              ListTile(
                                                onTap: () {
                                                  uploadCover();
                                                },
                                                leading: const Icon(
                                                    Icons.file_upload_outlined),
                                                title: const Text(
                                                    'Change Cover Picture'),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ))
                                    : showDialog(
                                        context: context,
                                        builder: (_) => AlertDialog(
                                              insetPadding: EdgeInsets.zero,
                                              contentPadding: EdgeInsets.zero,
                                              clipBehavior:
                                                  Clip.antiAliasWithSaveLayer,
                                              shape:
                                                  const RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.all(
                                                              Radius.circular(
                                                                  10.0))),
                                              content:
                                                  Builder(builder: (context) {
                                                return Image.network(
                                                  data.cover!,
                                                  fit: BoxFit.cover,
                                                );
                                              }),
                                            ));
                              },
                              child: SizedBox(
                                height: 200,
                                width: size,
                                child: Image.network(
                                  data.cover == null
                                      ? 'https://images.pexels.com/photos/255379/pexels-photo-255379.jpeg'
                                      : data.cover!,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: -80,
                              child: InkWell(
                                onTap: () {
                                  myID == data.userId
                                      ? Get.bottomSheet(Container(
                                          height: shimmerSize.height * .18,
                                          width: shimmerSize.width,
                                          decoration: const BoxDecoration(
                                            color: white,
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(
                                                horizontal: 15, vertical: 10.0),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.spaceAround,
                                              children: [
                                                ListTile(
                                                  onTap: () {
                                                    Navigator.pop(context);
                                                    showDialog(
                                                        context: context,
                                                        builder:
                                                            (_) => AlertDialog(
                                                                  insetPadding:
                                                                      EdgeInsets
                                                                          .zero,
                                                                  contentPadding:
                                                                      EdgeInsets
                                                                          .zero,
                                                                  clipBehavior:
                                                                      Clip.antiAliasWithSaveLayer,
                                                                  shape: const RoundedRectangleBorder(
                                                                      borderRadius:
                                                                          BorderRadius.all(
                                                                              Radius.circular(10.0))),
                                                                  content: Builder(
                                                                      builder:
                                                                          (context) {
                                                                    return Image
                                                                        .network(
                                                                      data.avatar!,
                                                                      fit: BoxFit
                                                                          .cover,
                                                                    );
                                                                  }),
                                                                ));
                                                  },
                                                  leading: const Icon(
                                                      Icons.remove_red_eye),
                                                  title: const Text(
                                                      'See profile picture'),
                                                ),
                                                ListTile(
                                                  onTap: () {
                                                    uploadProfile();
                                                  },
                                                  leading: const Icon(Icons
                                                      .file_upload_outlined),
                                                  title: const Text(
                                                      'Change Profile Picture'),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ))
                                      : showDialog(
                                          context: context,
                                          builder: (_) => AlertDialog(
                                                insetPadding: EdgeInsets.zero,
                                                contentPadding: EdgeInsets.zero,
                                                clipBehavior:
                                                    Clip.antiAliasWithSaveLayer,
                                                shape:
                                                    const RoundedRectangleBorder(
                                                        borderRadius:
                                                            BorderRadius.all(
                                                                Radius.circular(
                                                                    10.0))),
                                                content:
                                                    Builder(builder: (context) {
                                                  return Image.network(
                                                    data.avatar!,
                                                    fit: BoxFit.cover,
                                                  );
                                                }),
                                              ));
                                },
                                child: Container(
                                  height: 150,
                                  width: 150,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: background,
                                      width: 6,
                                    ),
                                    image: DecorationImage(
                                      image: NetworkImage(
                                        data.avatar == null
                                            ? 'https://png.pngtree.com/png-vector/20220807/ourmid/pngtree-man-avatar-wearing-gray-suit-png-image_6102786.png'
                                            : data.avatar!,
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                        sizedBox(90, 0),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              data.name!,
                              style: TextStyle(
                                  color: black.withOpacity(0.5),
                                  fontSize: 25,
                                  fontWeight: FontWeight.bold),
                            ),
                            sizedBox(0, 5),
                            data.isVerified == '1'
                                ? SvgPicture.asset(
                                    'assets/svg/verify.svg',
                                    width: 17,
                                  )
                                : Container(),
                            sizedBox(0, 2),
                            data.isPro == '1'
                                ? Image.asset(
                                    'assets/svg/vip.png',
                                    width: 18,
                                  )
                                : Container()
                          ],
                        ),
                        Text(
                          '${data.followerCount} followers . ${data.followingCount} following',
                          textAlign: TextAlign.center,
                          style: TextStyle(color: black.withOpacity(0.5)),
                        ),
                        if (myID == data.userId)
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20.0,
                              vertical: 10,
                            ),
                            child: InkWell(
                              onTap: () {
                                navigate('settings');
                              },
                              child: Container(
                                height: 50,
                                decoration: BoxDecoration(
                                  color: primary,
                                  borderRadius: BorderRadius.circular(40),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 15,
                                  ),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/svg/edit.svg',
                                        color: white,
                                      ),
                                      sizedBox(0, 10),
                                      const Center(
                                        child: Text(
                                          'Edit Profile',
                                          style: TextStyle(color: white),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        else
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 20.0,
                              vertical: 10,
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                InkWell(
                                  onTap: () {
                                    if (controller
                                            .getUserDetails.value.isFollowing ==
                                        1) {
                                      controller.statusChange(widget.userID, 0,
                                          controller.getUserDetails.value);
                                    } else {
                                      controller.statusChange(widget.userID, 1,
                                          controller.getUserDetails.value);
                                    }
                                  },
                                  child: Container(
                                    width: size * .4,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      color: primary,
                                      borderRadius: BorderRadius.circular(40),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 15,
                                      ),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            'assets/svg/follow.svg',
                                          ),
                                          sizedBox(0, 10),
                                          Center(
                                            child: Text(
                                              data.isFollowing == 1
                                                  ? 'Following'
                                                  : 'Follow',
                                              style:
                                                  const TextStyle(color: white),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                sizedBox(0, 10),
                                InkWell(
                                  onTap: () {
                                    Get.defaultDialog(
                                      title: 'Not Available',
                                      textCancel: 'Ok',
                                      cancelTextColor: primary,
                                      content: const Text(
                                          'Messaging future is not available right now.'),
                                    );
                                  },
                                  child: Container(
                                    width: size * .4,
                                    height: 50,
                                    decoration: BoxDecoration(
                                      border: Border.all(
                                          color: primary, width: 1.5),
                                      borderRadius: BorderRadius.circular(40),
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 15,
                                      ),
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          SvgPicture.asset(
                                            'assets/svg/message.svg',
                                            width: 20,
                                          ),
                                          sizedBox(0, 10),
                                          const Center(
                                            child: Text(
                                              'Chat',
                                              style: TextStyle(color: primary),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        const Divider(),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20.0, vertical: 10),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.home,
                                    color: Colors.grey.withOpacity(0.6),
                                  ),
                                  sizedBox(0, 10),
                                  data.address == ''
                                      ? Text('None',
                                          style: TextStyle(
                                              color:
                                                  Colors.grey.withOpacity(0.8)))
                                      : Text(
                                          data.address!,
                                          style: TextStyle(
                                              color:
                                                  Colors.grey.withOpacity(0.8)),
                                        )
                                ],
                              ),
                              sizedBox(5, 0),
                              Row(
                                children: [
                                  Icon(
                                    Icons.work,
                                    color: Colors.grey.withOpacity(0.6),
                                  ),
                                  sizedBox(0, 10),
                                  data.working == ''
                                      ? Text('None',
                                          style: TextStyle(
                                              color:
                                                  Colors.grey.withOpacity(0.8)))
                                      : Text(
                                          data.working!,
                                          style: TextStyle(
                                              color:
                                                  Colors.grey.withOpacity(0.8)),
                                        ),
                                ],
                              ),
                              sizedBox(5, 0),
                              Row(
                                children: [
                                  Icon(
                                    Icons.favorite,
                                    color: Colors.grey.withOpacity(0.6),
                                  ),
                                  sizedBox(0, 10),
                                  data.relationshipID == '1'
                                      ? Text(
                                          'Single',
                                          style: TextStyle(
                                              color:
                                                  Colors.grey.withOpacity(0.8)),
                                        )
                                      : data.relationshipID == '2'
                                          ? Text(
                                              'In a relationship',
                                              style: TextStyle(
                                                  color: Colors.grey
                                                      .withOpacity(0.8)),
                                            )
                                          : data.relationshipID == '3'
                                              ? Text(
                                                  'Married',
                                                  style: TextStyle(
                                                      color: Colors.grey
                                                          .withOpacity(0.8)),
                                                )
                                              : data.relationshipID == '4'
                                                  ? Text(
                                                      'Engaged',
                                                      style: TextStyle(
                                                          color: Colors.grey
                                                              .withOpacity(
                                                                  0.8)),
                                                    )
                                                  : Text(
                                                      'None',
                                                      style: TextStyle(
                                                          color: Colors.grey
                                                              .withOpacity(
                                                                  0.8)),
                                                    ),
                                ],
                              ),
                              sizedBox(5, 0),
                              InkWell(
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (_) => ProfileDetailsScreen(
                                                data: data,
                                              )));
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.more_horiz,
                                      color: Colors.grey.withOpacity(0.6),
                                    ),
                                    sizedBox(0, 10),
                                    Text(
                                      'More details about you.',
                                      style: TextStyle(
                                          color: Colors.grey.withOpacity(0.8)),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Divider(),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 20.0, vertical: 10),
                          child: Column(
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  RichText(
                                      text: TextSpan(
                                          text: 'Followers',
                                          style: TextStyle(
                                              color: black.withOpacity(0.5),
                                              fontSize: 20,
                                              fontWeight: FontWeight.w500),
                                          children: [
                                        TextSpan(
                                            text:
                                                (' (${data.followerCount} total)'),
                                            style: TextStyle(
                                              color: black.withOpacity(0.2),
                                            ))
                                      ])),
                                  InkWell(
                                    onTap: () {
                                      navigate('userFollowers');
                                    },
                                    child: const Text(
                                      'View All',
                                      style: TextStyle(
                                          color: primary,
                                          fontWeight: FontWeight.w400),
                                    ),
                                  )
                                ],
                              ),
                              sizedBox(10, 0),
                              data.followerCount == '0'
                                  ? Container()
                                  : FollowerSection(
                                      userID: widget.userID,
                                    )
                            ],
                          ),
                        ),
                        const Divider(),
                        if (myID == data.userId)
                          Center(
                            child: InkWell(
                              onTap: () {
                                navigate('newPost');
                              },
                              child: Container(
                                width: size * .9,
                                height: 180,
                                decoration: BoxDecoration(
                                  color: white,
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(15.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      const Divider(),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Container(
                                            height: 35,
                                            width: 35,
                                            decoration: BoxDecoration(
                                                shape: BoxShape.circle,
                                                image: DecorationImage(
                                                    image: NetworkImage(
                                                        data.avatar!),
                                                    fit: BoxFit.cover)),
                                          ),
                                          Center(
                                            child: Text(
                                              inMind,
                                            ),
                                          ),
                                          Container(
                                            height: 20,
                                            width: 20,
                                            decoration: const BoxDecoration(
                                              shape: BoxShape.circle,
                                            ),
                                            child: SvgPicture.asset(
                                              'assets/svg/smile.svg',
                                            ),
                                          ),
                                        ],
                                      ),
                                      const Divider(),
                                      Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Expanded(
                                              child: Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.grey.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Center(
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/svg/camera_icon.svg',
                                                      width: 17,
                                                    ),
                                                    Text(
                                                      'Photos',
                                                      style: TextStyle(
                                                          color: black
                                                              .withOpacity(0.5),
                                                          fontSize: 13),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          )),
                                          sizedBox(0, 5),
                                          Expanded(
                                              child: Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.grey.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Center(
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/svg/video_icon.svg',
                                                      width: 17,
                                                    ),
                                                    Text(
                                                      'Videos',
                                                      style: TextStyle(
                                                          color: black
                                                              .withOpacity(0.5),
                                                          fontSize: 13),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          )),
                                          sizedBox(0, 5),
                                          Expanded(
                                              child: Container(
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.grey.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(10),
                                            ),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.all(8.0),
                                              child: Center(
                                                child: Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceEvenly,
                                                  children: [
                                                    SvgPicture.asset(
                                                      'assets/svg/smile.svg',
                                                      width: 17,
                                                    ),
                                                    Text(
                                                      'Feelings',
                                                      style: TextStyle(
                                                          color: black
                                                              .withOpacity(0.5),
                                                          fontSize: 13),
                                                    )
                                                  ],
                                                ),
                                              ),
                                            ),
                                          )),
                                        ],
                                      ),
                                      sizedBox(10, 0),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                        else
                          Container(),
                        if (myID == data.userId)
                          const Divider()
                        else
                          Container(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20.0),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'Posts',
                                    style: TextStyle(
                                        color: black.withOpacity(0.5),
                                        fontSize: 20,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ],
                              ),
                            ),
                            Divider(
                              color: black.withOpacity(.1),
                            ),
                            sizedBox(10, 0),
                            UserPostSection(
                              userID: widget.userID,
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                );
        }
      }),
    );
  }
}
