// To parse this JSON data, do
//
//     final userFollowersModel = userFollowersModelFromJson(jsonString);

import 'dart:convert';

UserFollowersModel userFollowersModelFromJson(String str) => UserFollowersModel.fromJson(json.decode(str));

String userFollowersModelToJson(UserFollowersModel data) => json.encode(data.toJson());

class UserFollowersModel {
  int? apiStatus;
  List<Follower>? followers;

  UserFollowersModel({
    this.apiStatus,
    this.followers,
  });

  factory UserFollowersModel.fromJson(Map<String, dynamic> json) => UserFollowersModel(
    apiStatus: json["api_status"],
    followers: json["followers"] == null ? [] : List<Follower>.from(json["followers"]!.map((x) => Follower.fromJson(x))),
  );

  Map<String, dynamic> toJson() => {
    "api_status": apiStatus,
    "followers": followers == null ? [] : List<dynamic>.from(followers!.map((x) => x.toJson())),
  };
}

class Follower {
  String? userId;
  String? avatar;
  String? name;

  Follower({
    this.userId,
    this.avatar,
    this.name,
  });

  factory Follower.fromJson(Map<String, dynamic> json) => Follower(
    userId: json["user_id"],
    avatar: json["avatar"],
    name: json["name"],
  );

  Map<String, dynamic> toJson() => {
    "user_id": userId,
    "avatar": avatar,
    "name": name,
  };
}
